import { Header } from '@/components/layout/Header';
import { <PERSON>rad<PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/gradient-button';
import { Feature2 } from '@/components/feature-2';
import { DotPattern1 } from '@/components/ui/dot-pattern-1';
import { Feature197 } from '@/components/accordion-feature-section';
import { StatsSectionWithText } from '@/components/ui/stats-section-with-text';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import {
  Search,
  Home as HomeIcon,
  Users,
  TrendingUp,
  MapPin,
  Star,
  CheckCircle,
  ArrowRight
} from 'lucide-react';

export default function EnhancedHomePage() {
  const stats = [
    { label: 'Properties Listed', value: '10,000+' },
    { label: 'Happy Clients', value: '5,000+' },
    { label: 'Cities Covered', value: '50+' },
    { label: 'Years Experience', value: '15+' },
  ];

  const features = [
    {
      icon: Search,
      title: 'Advanced Property Search',
      description: 'Find your perfect home with our comprehensive MLS® integrated search tools.',
    },
    {
      icon: HomeIcon,
      title: 'Private Listings',
      description: 'List your property directly without agent fees and connect with buyers.',
    },
    {
      icon: Users,
      title: 'Professional Services',
      description: 'Access to lawyers, inspectors, photographers, and other real estate professionals.',
    },
    {
      icon: TrendingUp,
      title: 'Market Analytics',
      description: 'Get real-time market insights and property valuations.',
    },
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'Home Buyer',
      content: 'SoNo Brokers helped me find my dream home in Toronto. The search tools are amazing!',
      rating: 5,
    },
    {
      name: 'Mike Chen',
      role: 'Property Seller',
      content: 'Sold my condo without paying agent fees. The platform is user-friendly and effective.',
      rating: 5,
    },
    {
      name: 'Lisa Rodriguez',
      role: 'Real Estate Investor',
      content: 'The market analytics feature gives me the edge I need for investment decisions.',
      rating: 5,
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Hero Section */}
      <section className="relative pt-20 pb-16 overflow-hidden">
        <DotPattern1 className="absolute inset-0 opacity-20" />
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <Badge variant="outline" className="mb-6">
              🏠 Canada's Premier Real Estate Platform
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              Find Your Perfect Home
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Search MLS® listings, discover private sales, and connect with real estate professionals across Canada.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <GradientButton size="lg" className="px-8 py-4" asChild>
                <Link href="/properties">
                  <Search className="mr-2 h-5 w-5" />
                  Search Properties
                </Link>
              </GradientButton>
              <Button variant="outline" size="lg" className="px-8 py-4" asChild>
                <Link href="/list-property">
                  List Your Property
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <StatsSectionWithText
            title="Trusted by Thousands"
            description="Join the growing community of buyers and sellers who trust SoNo Brokers for their real estate needs."
            stats={stats}
          />
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Everything You Need for Real Estate
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              From searching properties to closing deals, we provide all the tools and services you need.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="bg-background">
                <CardHeader>
                  <feature.icon className="h-8 w-8 text-primary mb-4" />
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              What Our Clients Say
            </h2>
            <p className="text-xl text-muted-foreground">
              Real stories from real people who found success with SoNo Brokers.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-background">
                <CardHeader>
                  <div className="flex items-center gap-1 mb-2">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <CardTitle className="text-lg">{testimonial.name}</CardTitle>
                  <CardDescription>{testimonial.role}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">"{testimonial.content}"</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-muted-foreground">
              Get answers to common questions about buying, selling, and using our platform.
            </p>
          </div>
          <Feature197 features={[
            {
              id: 1,
              title: "How does SoNo Brokers work?",
              image: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop",
              description: "SoNo Brokers connects buyers and sellers directly while providing access to MLS® listings and professional services. You can search properties, list your home, and access real estate professionals all in one platform."
            },
            {
              id: 2,
              title: "Are there any fees for using the platform?",
              image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=800&h=600&fit=crop",
              description: "Basic searching and browsing is free. For private listings, we charge a small listing fee instead of traditional agent commissions, saving you thousands of dollars."
            },
            {
              id: 3,
              title: "Can I access MLS® listings?",
              image: "https://images.unsplash.com/photo-**********-ce09059eeffa?w=800&h=600&fit=crop",
              description: "Yes! We provide access to MLS® listings through our licensed brokerage partners across Canada, ensuring you see all available properties in your area."
            },
            {
              id: 4,
              title: "What professional services are available?",
              image: "https://images.unsplash.com/photo-1521791136064-7986c2920216?w=800&h=600&fit=crop",
              description: "We connect you with lawyers, home inspectors, photographers, mortgage brokers, and other real estate professionals to help with every aspect of your transaction."
            },
            {
              id: 5,
              title: "Which provinces do you serve?",
              image: "https://images.unsplash.com/photo-1517935706615-2717063c2225?w=800&h=600&fit=crop",
              description: "We currently serve Alberta, BC, Saskatchewan, Ontario, New Brunswick, and Nova Scotia through our licensed brokerage partners."
            }
          ]} />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join thousands of Canadians who have found their perfect home or sold their property with SoNo Brokers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="secondary" size="lg" className="px-8 py-4" asChild>
              <Link href="/properties">
                Start Searching
              </Link>
            </Button>
            <Button variant="outline" size="lg" className="px-8 py-4 border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary" asChild>
              <Link href="/list-property">
                List Your Property
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
