'use client'

import { RainbowButton } from '@/components/ui/rainbow-button'
import { useTheme } from 'next-themes'
import { useClerk, useUser } from '@clerk/nextjs'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

// A simple button to sign in with <PERSON>.
// It automatically redirects the user to callbackUrl (config.auth.callbackUrl) after login,
// which is normally a private page for users to manage their accounts.
// If the user is already logged in, it will show their profile picture & redirect them to callbackUrl immediately.
const ButtonSignin = ({
	text = 'Get started',
	extraStyle,
}: {
	text?: string
	extraStyle?: string
}) => {
	const router = useRouter()
	const { isSignedIn, user } = useUser()
	const { openSignIn, signOut } = useClerk()
	const { theme } = useTheme()

	const handleClick = () => {
		if (isSignedIn) {
			router.push('/')
		} else {
			openSignIn({
				// Optionally, you can specify sign-in options here
				redirectUrl: '/dashboard',
			})
		}
	}

	if (isSignedIn && user) {
		return (
			<Link href={'/dashboard'} legacyBehavior>
				<a className={`btn flex items-center gap-2`}>
					{user.hasImage ? (
						<Image
							src={user.imageUrl}
							alt={user.firstName || 'Account'}
							className='w-6 h-6 rounded-full shrink-0'
							referrerPolicy='no-referrer'
							width={24}
							height={24}
						/>
					) : (
						<span className='w-6 h-6 bg-base-300 flex justify-center items-center rounded-full shrink-0'>
							{user.firstName
								? user.firstName.charAt(0)
								: user.primaryEmailAddress?.emailAddress || 'A'}
						</span>
					)}
					{user.firstName || user.primaryEmailAddress?.emailAddress || 'Account'}
				</a>
			</Link>
		)
	}

	return (
		<RainbowButton
			theme={theme as 'light' | 'dark'}
			onClick={handleClick}
			className={`h-8 px-4 text-sm min-w-[100px] ${extraStyle || ''}`}
		>
			{text}
		</RainbowButton>
	)
}

export default ButtonSignin
