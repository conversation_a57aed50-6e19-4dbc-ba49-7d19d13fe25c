"use server";
import { currentUser } from '@clerk/nextjs/server';
import { prisma } from '@/libs/prisma';
import { UserType } from '@prisma/client';
import { useState } from 'react';

// Helper to get current user's role
async function getCurrentUserRole() {
  const user = await currentUser();
  if (!user) throw new Error('Not authenticated');
  const dbUser = await prisma.user.findUnique({ where: { email: user.emailAddresses[0].emailAddress } });
  if (!dbUser) throw new Error('User not found');
  return dbUser.userType;
}

// Admin: Add admin or product member
export async function addAdminOrProductMember(email: string, role: 'admin' | 'product') {
  const currentRole = await getCurrentUserRole();
  if (currentRole !== UserType.admin) throw new Error('Only admin can add admin/product members');
  if (!email) throw new Error('Email required');
  // Upsert user
  const user = await prisma.user.upsert({
    where: { email },
    update: { userType: UserType[role as keyof typeof UserType] },
    create: {
      email,
      fullName: email.split('@')[0],
      userType: UserType[role as keyof typeof UserType],
    },
  });
  return { success: true, user };
}

// Product: Add product member
export async function addProductMember(email: string) {
  const currentRole = await getCurrentUserRole();
  if (currentRole !== UserType.admin && currentRole !== UserType.product) throw new Error('Only admin/product can add product members');
  if (!email) throw new Error('Email required');
  // Upsert user
  const user = await prisma.user.upsert({
    where: { email },
    update: { userType: UserType.product },
    create: {
      email,
      fullName: email.split('@')[0],
      userType: UserType.product,
    },
  });
  return { success: true, user };
}

const [email, setEmail] = useState('');
const [role, setRole] = useState('product');
const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
const [message, setMessage] = useState('');

async function handleAddUser() {
  setStatus('idle');
  try {
    await addAdminOrProductMember(email, role as 'admin' | 'product'); // or addProductMember
    setStatus('success');
    setMessage('User added or upgraded successfully.');
  } catch (e) {
    setStatus('error');
    setMessage(e.message || 'Failed to add user.');
  }
}