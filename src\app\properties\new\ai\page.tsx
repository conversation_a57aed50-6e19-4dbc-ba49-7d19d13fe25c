'use client'

import { AddressPropertyCreator } from '@/components/properties/AddressPropertyCreator';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>rk<PERSON>, MapPin, Brain, TrendingUp } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function AIPropertyCreatorPage() {
    return (
        <div className="min-h-screen bg-gradient-to-br from-background via-muted/30 to-background">
            <div className="container mx-auto py-8 space-y-8">
                {/* Header */}
                <div className="text-center space-y-4">
                    <div className="flex items-center justify-center gap-2 mb-4">
                        <div className="p-3 rounded-full bg-primary/10">
                            <Sparkles className="h-8 w-8 text-primary" />
                        </div>
                    </div>
                    <h1 className="text-4xl font-bold">AI Property Creator</h1>
                    <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Transform any address into a comprehensive property listing with AI-powered insights and neighborhood reports.
                    </p>
                    <div className="flex items-center justify-center gap-2">
                        <Link href="/properties">
                            <Button variant="ghost" size="sm">← Back to Properties</Button>
                        </Link>
                    </div>
                </div>

                {/* Features Overview */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <Card className="text-center">
                        <CardContent className="pt-6">
                            <div className="p-3 rounded-full bg-blue-100 w-fit mx-auto mb-4">
                                <MapPin className="h-6 w-6 text-blue-600" />
                            </div>
                            <h3 className="font-semibold mb-2">Address Validation</h3>
                            <p className="text-sm text-muted-foreground">
                                Validate addresses using Google Maps API and get precise property coordinates.
                            </p>
                        </CardContent>
                    </Card>

                    <Card className="text-center">
                        <CardContent className="pt-6">
                            <div className="p-3 rounded-full bg-green-100 w-fit mx-auto mb-4">
                                <Brain className="h-6 w-6 text-green-600" />
                            </div>
                            <h3 className="font-semibold mb-2">AI-Generated Content</h3>
                            <p className="text-sm text-muted-foreground">
                                Create compelling property descriptions and comprehensive neighborhood reports.
                            </p>
                        </CardContent>
                    </Card>

                    <Card className="text-center">
                        <CardContent className="pt-6">
                            <div className="p-3 rounded-full bg-purple-100 w-fit mx-auto mb-4">
                                <TrendingUp className="h-6 w-6 text-purple-600" />
                            </div>
                            <h3 className="font-semibold mb-2">13 Report Types</h3>
                            <p className="text-sm text-muted-foreground">
                                Schools, shopping, dining, transit, healthcare, and more neighborhood insights.
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Main Creator Component */}
                <div className="max-w-4xl mx-auto">
                    <AddressPropertyCreator
                        onPropertyCreated={(property) => {
                            console.log('Property created:', property);
                            // In a real app, this would redirect to the new property or show success
                            alert('Property created successfully! Check the console for details.');
                        }}
                    />
                </div>

                {/* Sample Data Notice */}
                <Card className="max-w-4xl mx-auto bg-amber-50 border-amber-200">
                    <CardContent className="pt-6">
                        <div className="flex items-start gap-3">
                            <Sparkles className="h-5 w-5 text-amber-600 mt-0.5" />
                            <div>
                                <h4 className="font-medium text-amber-900 mb-1">
                                    Enhanced Sample Data Available
                                </h4>
                                <p className="text-sm text-amber-700">
                                    Try <strong>36 Heron Pl, Hamilton, ON L9A 4Y8</strong> for enhanced demo data with
                                    Hamilton-specific schools, shopping centers, restaurants, and local landmarks.
                                    This showcases the full potential of location-specific AI insights.
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Footer */}
                <div className="text-center py-8 border-t">
                    <p className="text-sm text-muted-foreground">
                        Powered by AI and Google Maps API for accurate property intelligence.
                    </p>
                </div>
            </div>
        </div>
    );
}
