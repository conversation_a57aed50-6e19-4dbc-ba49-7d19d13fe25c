import { prisma } from '../prisma';
import { ServiceBooking, BookingStatus, ServiceType } from '@prisma/client';

export class BookingService {
  static async createBooking(data: {
    propertyId: string;
    serviceProviderId: string;
    userId: string;
    serviceType: ServiceType;
    scheduledAt?: Date;
    notes?: string;
  }): Promise<ServiceBooking> {
    return prisma.serviceBooking.create({
      data,
    });
  }

  static async updateBooking(
    id: string,
    data: Partial<ServiceBooking>
  ): Promise<ServiceBooking> {
    return prisma.serviceBooking.update({
      where: { id },
      data,
    });
  }

  static async getBooking(id: string): Promise<ServiceBooking | null> {
    return prisma.serviceBooking.findUnique({
      where: { id },
      include: {
        property: true,
        serviceProvider: {
          include: {
            user: true,
          },
        },
        user: true,
      },
    });
  }

  static async listBookings(params: {
    skip?: number;
    take?: number;
    userId?: string;
    serviceProviderId?: string;
    propertyId?: string;
    status?: BookingStatus;
    serviceType?: ServiceType;
  }): Promise<ServiceBooking[]> {
    const { skip = 0, take = 10, ...filters } = params;

    return prisma.serviceBooking.findMany({
      skip,
      take,
      where: filters,
      include: {
        property: true,
        serviceProvider: {
          include: {
            user: true,
          },
        },
        user: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  static async updateBookingStatus(
    id: string,
    status: BookingStatus
  ): Promise<ServiceBooking> {
    return prisma.serviceBooking.update({
      where: { id },
      data: {
        status,
        completedAt: status === 'completed' ? new Date() : undefined,
      },
    });
  }

  static async getPropertyBookings(
    propertyId: string
  ): Promise<ServiceBooking[]> {
    return prisma.serviceBooking.findMany({
      where: { propertyId },
      include: {
        serviceProvider: {
          include: {
            user: true,
          },
        },
        user: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  static async getUserBookings(userId: string): Promise<ServiceBooking[]> {
    return prisma.serviceBooking.findMany({
      where: { userId },
      include: {
        property: true,
        serviceProvider: {
          include: {
            user: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
} 