const Wordpress = () => {
  return (
    <svg
      width="23"
      height="22"
      viewBox="0 0 23 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.98002 10.9497C1.98002 14.6476 4.1309 17.8325 7.2414 19.3464L2.79073 7.15258C2.2563 8.34714 1.98004 9.64105 1.98002 10.9497ZM11.3115 20.2812C12.3952 20.2812 13.4376 20.0909 14.4138 19.7518L14.3476 19.6277L11.477 11.7687L8.68083 19.9007C9.50809 20.1489 10.3933 20.2812 11.3115 20.2812ZM12.5938 6.5735L15.969 16.6082L16.9038 13.4977C17.3009 12.2071 17.607 11.2806 17.607 10.4782C17.607 9.32 17.1933 8.52583 16.8459 7.91366C16.3661 7.13603 15.9276 6.4825 15.9276 5.72142C15.9276 4.86107 16.5729 4.06689 17.4994 4.06689H17.6153C15.8958 2.48841 13.6456 1.61431 11.3115 1.6182C9.76604 1.61791 8.24473 2.00182 6.88445 2.7354C5.52416 3.46897 4.36757 4.52919 3.51872 5.82069L4.11435 5.83723C5.09052 5.83723 6.59614 5.71314 6.59614 5.71314C7.10904 5.68833 7.16694 6.42459 6.66232 6.4825C6.66232 6.4825 6.15769 6.54868 5.58688 6.5735L8.99519 16.6826L11.0385 10.5609L9.58255 6.5735C9.25642 6.5555 8.93089 6.52791 8.60638 6.49077C8.10175 6.45768 8.15966 5.68833 8.66429 5.71314C8.66429 5.71314 10.203 5.83723 11.1213 5.83723C12.0974 5.83723 13.603 5.71314 13.603 5.71314C14.1077 5.68833 14.1738 6.42459 13.6692 6.4825C13.6692 6.4825 13.1646 6.54041 12.5938 6.5735ZM16.0021 19.0155C17.4138 18.1947 18.5853 17.0178 19.3997 15.6024C20.2141 14.187 20.6428 12.5827 20.643 10.9497C20.643 9.32828 20.2294 7.80612 19.5014 6.47423C19.6492 7.93954 19.4274 9.41853 18.8561 10.776L16.0021 19.0155ZM11.3115 21.7041C8.45928 21.7041 5.72386 20.5711 3.70702 18.5542C1.69018 16.5374 0.557129 13.802 0.557129 10.9497C0.557129 8.09746 1.69018 5.36204 3.70702 3.3452C5.72386 1.32836 8.45928 0.195313 11.3115 0.195312C14.1638 0.195313 16.8992 1.32836 18.916 3.3452C20.9329 5.36204 22.0659 8.09746 22.0659 10.9497C22.0659 13.802 20.9329 16.5374 18.916 18.5542C16.8992 20.5711 14.1638 21.7041 11.3115 21.7041Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default Wordpress;
