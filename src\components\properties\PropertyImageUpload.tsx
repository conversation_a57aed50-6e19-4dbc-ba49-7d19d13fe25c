'use client';

import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useUser } from '@clerk/nextjs';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'sonner';

interface PropertyImageUploadProps {
  propertyId: string;
  propertyName: string;
  onUploadComplete: (imageUrl: string) => void;
}

export function PropertyImageUpload({ propertyId, propertyName, onUploadComplete }: PropertyImageUploadProps) {
  const { user } = useUser();
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const generateUniqueFileName = (file: File) => {
    const timestamp = new Date().getTime();
    const uniqueId = uuidv4();
    const sanitizedPropertyName = propertyName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const sanitizedEmail = user?.primaryEmailAddress?.emailAddress?.replace(/[^a-z0-9]/gi, '_').toLowerCase() || 'anonymous';
    const extension = file.name.split('.').pop();
    return `${sanitizedEmail}/${sanitizedPropertyName}/${timestamp}_${uniqueId}.${extension}`;
  };

  const uploadToAzure = async (file: File) => {
    try {
      // Get SAS token from your API
      const response = await fetch('/api/upload/get-sas-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: generateUniqueFileName(file),
          contentType: file.type,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get SAS token');
      }

      const { sasUrl } = await response.json();

      // Upload to Azure Blob Storage
      const uploadResponse = await fetch(sasUrl, {
        method: 'PUT',
        headers: {
          'x-ms-blob-content-type': file.type,
          'x-ms-blob-type': 'BlockBlob',
        },
        body: file,
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload to Azure');
      }

      // Get the blob URL from the SAS URL
      const blobUrl = sasUrl.split('?')[0];
      return blobUrl;
    } catch (error) {
      console.error('Upload error:', error);
      throw error;
    }
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    setIsUploading(true);

    try {
      // Create preview
      const preview = URL.createObjectURL(file);
      setPreviewUrl(preview);

      // Upload to Azure
      const imageUrl = await uploadToAzure(file);
      onUploadComplete(imageUrl);
      toast.success('Image uploaded successfully');
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  }, [onUploadComplete, propertyName, user]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    },
    maxSize: 5 * 1024 * 1024, // 5MB
    multiple: false
  });

  return (
    <div className="w-full">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors
          ${isDragActive ? 'border-primary bg-primary/5' : 'border-gray-300 hover:border-primary'}`}
      >
        <input {...getInputProps()} />
        {previewUrl ? (
          <div className="relative w-full h-48">
            <Image
              src={previewUrl}
              alt="Property preview"
              fill
              className="object-contain rounded-lg"
            />
          </div>
        ) : (
          <div className="py-8">
            <p className="text-sm text-gray-500">
              {isDragActive
                ? 'Drop the image here'
                : 'Drag and drop an image, or click to select'}
            </p>
            <p className="text-xs text-gray-400 mt-1">
              Supported formats: JPEG, PNG, WebP (max 5MB)
            </p>
          </div>
        )}
      </div>
      {isUploading && (
        <div className="mt-2 text-sm text-gray-500">
          Uploading...
        </div>
      )}
    </div>
  );
} 