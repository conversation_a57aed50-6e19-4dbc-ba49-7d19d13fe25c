import { prisma } from '../prisma';
import { PropertyViewing } from '@prisma/client';

export class PropertyViewingService {
  static async requestViewing(data: {
    propertyId: string;
    buyerId: string;
    proposedTimes: any;
    notes?: string;
  }): Promise<PropertyViewing> {
    return prisma.propertyViewing.create({
      data,
    });
  }

  static async updateViewing(
    id: string,
    data: Partial<PropertyViewing>
  ): Promise<PropertyViewing> {
    return prisma.propertyViewing.update({
      where: { id },
      data,
    });
  }

  static async getViewing(id: string): Promise<PropertyViewing | null> {
    return prisma.propertyViewing.findUnique({
      where: { id },
      include: {
        property: true,
        buyer: true,
      },
    });
  }

  static async listPropertyViewings(
    propertyId: string
  ): Promise<PropertyViewing[]> {
    return prisma.propertyViewing.findMany({
      where: { propertyId },
      include: {
        buyer: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  static async listBuyerViewings(
    buyerId: string
  ): Promise<PropertyViewing[]> {
    return prisma.propertyViewing.findMany({
      where: { buyerId },
      include: {
        property: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  static async confirmViewing(
    id: string,
    confirmedTime: Date
  ): Promise<PropertyViewing> {
    return prisma.propertyViewing.update({
      where: { id },
      data: {
        status: 'confirmed',
        confirmedTime,
      },
    });
  }

  static async cancelViewing(id: string): Promise<PropertyViewing> {
    return prisma.propertyViewing.update({
      where: { id },
      data: {
        status: 'cancelled',
      },
    });
  }

  static async completeViewing(id: string): Promise<PropertyViewing> {
    return prisma.propertyViewing.update({
      where: { id },
      data: {
        status: 'completed',
      },
    });
  }
} 