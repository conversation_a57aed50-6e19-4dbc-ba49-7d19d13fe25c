'use client';

import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

interface OpenHouse {
    id: string;
    propertyId: string;
    property: {
        id: string;
        title: string;
        address: string;
        price: number;
        bedrooms: number;
        bathrooms: number;
        sqft?: number;
        propertyType: string;
        coordinates: {
            lat: number;
            lng: number;
        };
        images?: { url: string; alt?: string }[];
    };
    date: string;
    startTime: string;
    endTime: string;
    description?: string;
    registeredCount: number;
    maxAttendees: number;
    hostName: string;
}

interface OpenHouseMapProps {
    openHouses: OpenHouse[];
    onMapMove: (bounds: mapboxgl.LngLatBounds) => void;
    onOpenHouseClick?: (openHouse: OpenHouse) => void;
    initialCenter?: { lat: number; lng: number };
    initialZoom?: number;
}

const OpenHouseMap: React.FC<OpenHouseMapProps> = ({
    openHouses,
    onMapMove,
    onOpenHouseClick,
    initialCenter = { lat: 43.6532, lng: -79.3832 }, // Default to Toronto
    initialZoom = 11,
}) => {
    const mapRef = useRef<HTMLDivElement>(null);
    const map = useRef<mapboxgl.Map | null>(null);
    const markers = useRef<mapboxgl.Marker[]>([]);

    // Clear existing markers
    const clearMarkers = () => {
        markers.current.forEach(marker => marker.remove());
        markers.current = [];
    };

    // Initialize map
    useEffect(() => {
        if (!mapRef.current) return;

        // Set Mapbox access token
        mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY || 'pk.eyJ1IjoiamF2aWFucGljYXJkbzMzIiwiYSI6ImNtYjY4ZGRyazBiaWYybHEyMWpnNGN4cDQifQ.m63aGFvbfzrQhT3sWlSbDQ';

        // Initialize map
        map.current = new mapboxgl.Map({
            container: mapRef.current,
            style: 'mapbox://styles/mapbox/streets-v12',
            center: [initialCenter.lng, initialCenter.lat],
            zoom: initialZoom,
        });

        // Add map controls
        map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');
        map.current.addControl(new mapboxgl.FullscreenControl(), 'top-right');

        // Handle map move events
        const handleMoveEnd = () => {
            if (map.current) {
                const bounds = map.current.getBounds();
                onMapMove(bounds);
            }
        };

        map.current.on('moveend', handleMoveEnd);

        return () => {
            if (map.current) {
                map.current.off('moveend', handleMoveEnd);
                map.current.remove();
            }
        };
    }, [initialCenter, initialZoom, onMapMove]);

    // Update markers when open houses change
    useEffect(() => {
        if (!map.current) return;

        clearMarkers();

        openHouses.forEach((openHouse) => {
            if (openHouse.property.coordinates) {
                // Format date and time
                const date = new Date(openHouse.date).toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                });
                const startTime = new Date(`2000-01-01T${openHouse.startTime}`).toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                });
                const endTime = new Date(`2000-01-01T${openHouse.endTime}`).toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                });

                // Create custom marker element for open house
                const markerElement = document.createElement('div');
                markerElement.className = 'open-house-marker';
                markerElement.innerHTML = `
                    <div class="bg-green-600 text-white px-3 py-2 rounded-lg shadow-lg border-2 border-white cursor-pointer hover:bg-green-700 transition-colors">
                        <div class="text-xs font-semibold">${date}</div>
                        <div class="text-xs">${startTime}</div>
                        <div class="text-xs font-bold">$${openHouse.property.price.toLocaleString()}</div>
                    </div>
                `;

                // Create popup content
                const popupContent = `
                    <div class="p-3 max-w-sm">
                        <div class="mb-2">
                            <h3 class="font-semibold text-sm text-gray-900">${openHouse.property.title}</h3>
                            <p class="text-xs text-gray-600">${openHouse.property.address}</p>
                        </div>
                        
                        <div class="mb-3">
                            <div class="text-lg font-bold text-green-600">$${openHouse.property.price.toLocaleString()}</div>
                            <div class="text-xs text-gray-600">
                                ${openHouse.property.bedrooms} bed • ${openHouse.property.bathrooms} bath
                                ${openHouse.property.sqft ? ` • ${openHouse.property.sqft.toLocaleString()} sqft` : ''}
                            </div>
                        </div>

                        <div class="mb-3 p-2 bg-green-50 rounded">
                            <div class="text-sm font-semibold text-green-800">Open House</div>
                            <div class="text-xs text-green-700">${date}</div>
                            <div class="text-xs text-green-700">${startTime} - ${endTime}</div>
                            <div class="text-xs text-green-600 mt-1">
                                ${openHouse.registeredCount}/${openHouse.maxAttendees} registered
                            </div>
                        </div>

                        <div class="mb-2">
                            <div class="text-xs text-gray-600">Host: ${openHouse.hostName}</div>
                        </div>

                        ${openHouse.description ? `
                            <div class="mb-3">
                                <p class="text-xs text-gray-700">${openHouse.description}</p>
                            </div>
                        ` : ''}

                        <div class="flex gap-2">
                            <button 
                                onclick="window.open('/properties/${openHouse.property.id}', '_blank')"
                                class="flex-1 bg-blue-600 text-white text-xs px-2 py-1 rounded hover:bg-blue-700 transition-colors"
                            >
                                View Property
                            </button>
                            <button 
                                onclick="alert('Registration feature coming soon!')"
                                class="flex-1 bg-green-600 text-white text-xs px-2 py-1 rounded hover:bg-green-700 transition-colors"
                            >
                                Register
                            </button>
                        </div>
                    </div>
                `;

                const marker = new mapboxgl.Marker(markerElement)
                    .setLngLat([openHouse.property.coordinates.lng, openHouse.property.coordinates.lat])
                    .setPopup(
                        new mapboxgl.Popup({
                            offset: 25,
                            closeButton: true,
                            closeOnClick: false
                        }).setHTML(popupContent)
                    )
                    .addTo(map.current);

                // Handle marker click
                markerElement.addEventListener('click', () => {
                    if (onOpenHouseClick) {
                        onOpenHouseClick(openHouse);
                    }
                });

                markers.current.push(marker);
            }
        });

        // Fit map to show all markers if there are any
        if (openHouses.length > 0 && markers.current.length > 0) {
            const bounds = new mapboxgl.LngLatBounds();
            openHouses.forEach(openHouse => {
                if (openHouse.property.coordinates) {
                    bounds.extend([openHouse.property.coordinates.lng, openHouse.property.coordinates.lat]);
                }
            });
            
            if (!bounds.isEmpty()) {
                map.current?.fitBounds(bounds, {
                    padding: 50,
                    maxZoom: 15
                });
            }
        }
    }, [openHouses, onOpenHouseClick]);

    return (
        <div className="relative h-full w-full">
            <div
                ref={mapRef}
                className="h-full w-full"
            />
            
            {/* Map Legend */}
            <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 z-10">
                <h4 className="text-sm font-semibold mb-2">Open Houses</h4>
                <div className="flex items-center gap-2 text-xs">
                    <div className="w-4 h-4 bg-green-600 rounded"></div>
                    <span>Available Open House</span>
                </div>
                <div className="text-xs text-gray-600 mt-1">
                    Click markers for details
                </div>
            </div>

            {/* Open House Count */}
            <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 z-10">
                <div className="text-sm font-semibold text-green-600">
                    {openHouses.length} Open Houses
                </div>
                <div className="text-xs text-gray-600">
                    in this area
                </div>
            </div>
        </div>
    );
};

export default OpenHouseMap;
