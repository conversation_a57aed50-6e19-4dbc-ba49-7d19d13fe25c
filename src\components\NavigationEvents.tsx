'use client'

import { useEffect } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'

export function NavigationEvents(): JSX.Element {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    const url = `${pathname}${searchParams.size > 0 ? `?${searchParams}` : ''}`

    // Log page view (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.log(`Page viewed: ${url}`)
    }

    // You could send this to analytics
    const pageData = {
      path: pathname,
      search: searchParams.toString(),
      timestamp: new Date().toISOString(),
      country: typeof window !== 'undefined' ? localStorage.getItem('userCountry') || 'unknown' : 'unknown'
    }

    // Example analytics call
    // analytics.trackPageView(pageData)

    // You could also update UI based on the current URL
    if (typeof document !== 'undefined') {
      const pageName = pathname.split('/').filter(Boolean).pop() || 'Home'
      document.title = `SoNo Brokers | ${pageName.charAt(0).toUpperCase() + pageName.slice(1)}`
    }

  }, [pathname, searchParams])

  return null
}
