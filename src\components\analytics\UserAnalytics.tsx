'use client'

import { Card, CardContent } from "@/components/ui/card"
import { formatDate } from "@/lib/utils"

type ActivityType = 'view' | 'inquiry' | 'property_updated' | 'message_received'

interface Activity {
  id: string
  type: ActivityType
  timestamp: string
  propertyId: string
  propertyTitle: string
  message?: string
}

interface UserAnalyticsProps {
  analytics: {
    totalProperties: number
    activeListings: number
    totalViews: number
    totalInquiries: number
    averageResponseTime: number
    recentActivity: Activity[]
  }
}

export function UserAnalytics({ analytics }: UserAnalyticsProps) {
  const { totalProperties, activeListings, totalViews, totalInquiries, averageResponseTime, recentActivity } = analytics

  const getActivityIcon = (type: ActivityType) => {
    switch (type) {
      case 'view':
        return '👁️'
      case 'inquiry':
        return '❓'
      case 'property_updated':
        return '🔄'
      case 'message_received':
        return '💬'
      default:
        return '📝'
    }
  }

  const getActivityText = (activity: Activity) => {
    switch (activity.type) {
      case 'view':
        return `Someone viewed "${activity.propertyTitle}"`
      case 'inquiry':
        return `New inquiry for "${activity.propertyTitle}"`
      case 'property_updated':
        return `You updated "${activity.propertyTitle}"`
      case 'message_received':
        return `New message about "${activity.propertyTitle}"`
      default:
        return `Activity on "${activity.propertyTitle}"`
    }
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{totalProperties}</div>
            <p className="text-sm text-muted-foreground">Total Properties</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{activeListings}</div>
            <p className="text-sm text-muted-foreground">Active Listings</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{totalViews}</div>
            <p className="text-sm text-muted-foreground">Total Views</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{averageResponseTime}m</div>
            <p className="text-sm text-muted-foreground">Avg. Response Time</p>
          </CardContent>
        </Card>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-2">Recent Activity</h3>
        <div className="space-y-2">
          {recentActivity.map(activity => (
            <div key={activity.id} className="flex items-start p-3 border rounded-md">
              <div className="mr-3 text-xl">{getActivityIcon(activity.type)}</div>
              <div>
                <p className="font-medium">{getActivityText(activity)}</p>
                <p className="text-sm text-muted-foreground">{formatDate(activity.timestamp)}</p>
                {activity.message && (
                  <p className="text-sm mt-1 p-2 bg-muted rounded-md">{activity.message}</p>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
} 
