"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Suspense } from "react";
import Header from "@/components/Header";
import Hero from "@/components/Hero";
import Features from "@/components/Features";
import Pricing from "@/components/Pricing";
import Faq from "@/components/FAQ";
import CTA from "@/components/CTA";
import Footer from "@/components/Footer";
import Comparison from "@/components/Comparison";
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const GEO_API = "https://ipapi.co/json/"; // You can swap for another API if needed

const SUPPORTED_COUNTRIES: Record<string, string> = {
  CA: "/ca",
  US: "/us",
};

export default function HomePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [geoError, setGeoError] = useState(false);
  const [manual, setManual] = useState(false);

  useEffect(() => {
    fetch(GEO_API)
      .then((res) => res.json())
      .then((data) => {
        const country = data?.country_code as string;
        if (country && SUPPORTED_COUNTRIES[country]) {
          router.replace(SUPPORTED_COUNTRIES[country]);
        } else if (country) {
          router.replace("/unsupported-region");
        } else {
          setGeoError(true);
          setManual(true);
        }
      })
      .catch(() => {
        setGeoError(true);
        setManual(true);
      })
      .finally(() => setLoading(false));
  }, [router]);

  if (loading && !manual) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="animate-pulse text-3xl font-bold text-foreground tracking-wide">Loading SoNoBrokers...</div>
      </div>
    );
  }

  if (manual) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-background">
        <div className="mb-8 text-4xl font-extrabold text-foreground tracking-tight">Select Your Country</div>
        <div className="flex gap-6">
          <button
            className="px-8 py-4 rounded-xl bg-card hover:bg-accent text-card-foreground hover:text-accent-foreground text-xl font-semibold shadow-lg transition-all duration-200 border border-border backdrop-blur"
            onClick={() => router.replace("/ca")}
          >
            Canada
          </button>
          <button
            className="px-8 py-4 rounded-xl bg-card hover:bg-accent text-card-foreground hover:text-accent-foreground text-xl font-semibold shadow-lg transition-all duration-200 border border-border backdrop-blur"
            onClick={() => router.replace("/us")}
          >
            USA
          </button>
        </div>
        {geoError && (
          <div className="mt-8 text-destructive text-lg">Could not detect your country automatically.</div>
        )}
      </div>
    );
  }

  return null;
}