import Image from "next/image";
import logo from "@/assets/images/logo.svg";

const Logo = ({ isLarge = false }) => {
  return (
    <div className="flex items-center gap-[1px]"> {/* 1px gap for logo and text */}
      <Image
        src={logo}
        width={24}
        height={24}
        layout="lazy"
        alt="logo"
        className={`${isLarge ? `hidden` : ""}`}
      />
      <div className="flex gap-[1px] items-center"> {/* 1px gap between "SoNo" and "Brokers" */}
        <p
          className={`text-primary dark:text-primary ${ /* Changed text color to primary (green) */
            isLarge
              ? "text-[20px] sm:text-[32px] leading-[20px] sm:leading-[32px] font-bold"
              : "text-[20px] leading-[20px] font-semibold"
            }`}
        >
          SoNo
        </p>
        <p
          className={`text-primary dark:text-primary bg-transparent font-semibold px-0 rounded-[0px] pb-[0px] pt-[0px] ${ /* Changed text color to primary (green) and removed background/padding */
            isLarge
              ? "text-[20px] sm:text-[32px] leading-[20px] sm:leading-[32px]"
              : "text-[20px] leading-[20px]"
            }`}
        >
          Brokers
        </p>
      </div>
    </div>
  );
};

export default Logo;
