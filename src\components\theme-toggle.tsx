'use client'

import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'
import { ThemeToggle as UIThemeToggle } from '@/components/ui/theme-toggle'

export function ThemeToggle({ className }: { className?: string }) {
  const { theme, setTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // Wait for component to mount to avoid hydration mismatch
  useEffect(() => {
    setMounted(true)
    // Set default theme to light if not already set
    if (!theme) {
      setTheme('light')
    }
  }, [theme, setTheme])

  if (!mounted) {
    return <div className="w-16 h-8 opacity-0" />
  }

  // Create a wrapper that integrates the UI toggle with next-themes
  const handleToggle = () => {
    const currentTheme = resolvedTheme || theme
    setTheme(currentTheme === 'dark' ? 'light' : 'dark')
  }

  return (
    <div onClick={handleToggle} className={className}>
      <UIThemeToggle />
    </div>
  )
}

function DefaultToggle() {
  return (
    <div className="space-y-2 text-center">
      <div className="flex justify-center">
        <ThemeToggle />
      </div>
    </div>
  )
}

export { DefaultToggle }
