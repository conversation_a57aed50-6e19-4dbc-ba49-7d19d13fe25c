const NextJs = () => {
  return (
    <svg
      width="21"
      height="22"
      viewBox="0 0 21 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_521_32457)">
        <mask
          id="mask0_521_32457"
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="21"
          height="22"
        >
          <path
            d="M20.3646 0.912109H0V21.2767H20.3646V0.912109Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask0_521_32457)">
          <path
            d="M10.1837 20.4812C15.3679 20.4812 19.5705 16.2785 19.5705 11.0943C19.5705 5.91014 15.3679 1.70752 10.1837 1.70752C4.9995 1.70752 0.796875 5.91014 0.796875 11.0943C0.796875 16.2785 4.9995 20.4812 10.1837 20.4812Z"
            fill="black"
            stroke="black"
            strokeWidth="0.135764"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path d="M13.5312 14.9126V7.27588V14.9126Z" fill="black" />
          <path
            d="M13.5312 14.9126V7.27588"
            stroke="url(#paint0_linear_521_32457)"
            strokeWidth="0.135764"
            strokeMiterlimit="1.41421"
            strokeLinejoin="round"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M7.63005 7.27699L6.03906 7.27588V14.9126H7.63005V9.7807L15.7333 19.6316C16.1794 19.341 16.6012 19.0163 16.9952 18.6614L7.63005 7.27699Z"
            fill="url(#paint1_linear_521_32457)"
          />
        </g>
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_521_32457"
          x1="-nan"
          y1="-nan"
          x2="-nan"
          y2="-nan"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" />
          <stop offset="0.609375" stopColor="white" stopOpacity="0.57" />
          <stop offset="0.796875" stopColor="white" stopOpacity="0" />
          <stop offset="1" stopColor="white" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_521_32457"
          x1="12.648"
          y1="12.4466"
          x2="17.2554"
          y2="17.9663"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" />
          <stop offset="1" stopColor="white" stopOpacity="0" />
        </linearGradient>
        <clipPath id="clip0_521_32457">
          <rect
            width="20.3646"
            height="20.3646"
            fill="white"
            transform="translate(0 0.912109)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default NextJs;
