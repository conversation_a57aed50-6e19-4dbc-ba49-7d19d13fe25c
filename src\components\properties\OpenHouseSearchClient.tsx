'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import OpenHouseMap from './OpenHouseMap';
import { But<PERSON> } from '@/components/ui/button';
import { OpenHouseCard } from '@/components/ui/open-house-card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import {
    Calendar,
    MapPin,
    Filter,
    X,
    Grid3X3,
    Map as MapIcon,
    Clock,
    Home,
    Search,
    Slide<PERSON>H<PERSON>zontal,
    ChevronDown,
    ChevronUp
} from 'lucide-react';

interface OpenHouse {
    id: string;
    propertyId: string;
    property: {
        id: string;
        title: string;
        address: string;
        price: number;
        bedrooms: number;
        bathrooms: number;
        sqft?: number;
        propertyType: string;
        coordinates: {
            lat: number;
            lng: number;
        };
        images?: { url: string; alt?: string }[];
    };
    date: string;
    startTime: string;
    endTime: string;
    description?: string;
    registeredCount: number;
    maxAttendees: number;
    hostName: string;
    hostPhone?: string;
    hostEmail?: string;
}

interface OpenHouseSearchClientProps {
    initialOpenHouses: OpenHouse[];
}

export function OpenHouseSearchClient({ initialOpenHouses }: OpenHouseSearchClientProps) {
    const router = useRouter();
    const searchParams = useSearchParams();
    const { user } = useUser();

    // Search and filter states
    const [openHouses, setOpenHouses] = useState<OpenHouse[]>(initialOpenHouses);
    const [loading, setLoading] = useState(false);
    const [searchQuery, setSearchQuery] = useState(searchParams.get('location') || '');
    const [dateFrom, setDateFrom] = useState(searchParams.get('dateFrom') || '');
    const [dateTo, setDateTo] = useState(searchParams.get('dateTo') || '');
    const [propertyType, setPropertyType] = useState(searchParams.get('propertyType') || '');
    const [priceRange, setPriceRange] = useState([
        parseInt(searchParams.get('minPrice') || '0'),
        parseInt(searchParams.get('maxPrice') || '2000000')
    ]);
    const [bedroomRange, setBedroomRange] = useState([
        parseInt(searchParams.get('minBedrooms') || '0'),
        parseInt(searchParams.get('maxBedrooms') || '10')
    ]);
    const [distanceRange, setDistanceRange] = useState([
        parseInt(searchParams.get('distance') || '10')
    ]);

    // UI states
    const [activeViews, setActiveViews] = useState<Set<'grid' | 'map'>>(new Set(['map', 'grid']));
    const [isFiltersVisible, setIsFiltersVisible] = useState(false);
    const [mapCenter, setMapCenter] = useState({ lat: 43.6532, lng: -79.3832 }); // Toronto default
    const [mapBounds, setMapBounds] = useState<any>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const openHousesPerPage = 10;

    // Mock data for development
    useEffect(() => {
        const mockOpenHouses: OpenHouse[] = [
            {
                id: '1',
                propertyId: 'prop1',
                property: {
                    id: 'prop1',
                    title: 'Modern Downtown Condo',
                    address: '123 King St W, Toronto, ON',
                    price: 650000,
                    bedrooms: 2,
                    bathrooms: 2,
                    sqft: 1200,
                    propertyType: 'Condo',
                    coordinates: { lat: 43.6426, lng: -79.3871 },
                    images: [{ url: '/api/placeholder/400/300', alt: 'Modern condo' }]
                },
                date: '2025-02-01',
                startTime: '14:00',
                endTime: '16:00',
                description: 'Beautiful modern condo with stunning city views',
                registeredCount: 8,
                maxAttendees: 20,
                hostName: 'Sarah Johnson',
                hostPhone: '(*************',
                hostEmail: '<EMAIL>'
            },
            {
                id: '2',
                propertyId: 'prop2',
                property: {
                    id: 'prop2',
                    title: 'Family Home in Suburbs',
                    address: '456 Oak Ave, Mississauga, ON',
                    price: 850000,
                    bedrooms: 4,
                    bathrooms: 3,
                    sqft: 2400,
                    propertyType: 'House',
                    coordinates: { lat: 43.5890, lng: -79.6441 },
                    images: [{ url: '/api/placeholder/400/300', alt: 'Family home' }]
                },
                date: '2025-02-02',
                startTime: '13:00',
                endTime: '15:00',
                description: 'Perfect family home with large backyard',
                registeredCount: 12,
                maxAttendees: 25,
                hostName: 'Mike Chen',
                hostPhone: '(*************',
                hostEmail: '<EMAIL>'
            }
        ];
        setOpenHouses(mockOpenHouses);
    }, []);

    const handleSearch = useCallback(async () => {
        setLoading(true);
        try {
            // Build query parameters
            const params = new URLSearchParams();
            if (searchQuery) params.set('location', searchQuery);
            if (dateFrom) params.set('dateFrom', dateFrom);
            if (dateTo) params.set('dateTo', dateTo);
            if (propertyType) params.set('propertyType', propertyType);
            if (priceRange[0] > 0) params.set('minPrice', priceRange[0].toString());
            if (priceRange[1] < 2000000) params.set('maxPrice', priceRange[1].toString());
            if (bedroomRange[0] > 0) params.set('minBedrooms', bedroomRange[0].toString());
            if (bedroomRange[1] < 10) params.set('maxBedrooms', bedroomRange[1].toString());
            if (distanceRange[0] !== 10) params.set('distance', distanceRange[0].toString());

            // Update URL
            router.push(`/properties/open-houses?${params.toString()}`);

            // TODO: Replace with actual API call
            // const response = await fetch(`/api/open-houses/search?${params.toString()}`);
            // const data = await response.json();
            // setOpenHouses(data.openHouses);
            // setTotalPages(Math.ceil(data.total / openHousesPerPage));
        } catch (error) {
            console.error('Error searching open houses:', error);
        } finally {
            setLoading(false);
        }
    }, [searchQuery, dateFrom, dateTo, propertyType, priceRange, bedroomRange, distanceRange, router]);

    const handleMapMove = useCallback((bounds: any) => {
        // Use a ref to avoid infinite re-renders
        setMapBounds(bounds);
        // TODO: Filter open houses by map bounds
    }, []);

    const toggleView = (view: 'grid' | 'map') => {
        const newViews = new Set(activeViews);
        if (newViews.has(view)) {
            if (newViews.size > 1) {
                newViews.delete(view);
            }
        } else {
            newViews.add(view);
        }
        setActiveViews(newViews);
    };

    const clearFilters = () => {
        setSearchQuery('');
        setDateFrom('');
        setDateTo('');
        setPropertyType('');
        setPriceRange([0, 2000000]);
        setBedroomRange([0, 10]);
        setDistanceRange([10]);
        router.push('/properties/open-houses');
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatTime = (timeString: string) => {
        return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    };

    return (
        <div className="flex flex-col h-screen bg-background">
            {/* Header */}
            <div className="bg-background border-b border-border p-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <h1 className="text-2xl font-bold flex items-center gap-2">
                            <Calendar className="w-6 h-6 text-primary" />
                            Open Houses
                        </h1>
                        <Badge variant="secondary" className="text-sm">
                            {openHouses.length} available
                        </Badge>
                    </div>

                    {/* View Toggle */}
                    <div className="flex items-center gap-2">
                        <Button
                            variant={activeViews.has('map') ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => toggleView('map')}
                            className="h-8 px-3"
                        >
                            <MapIcon className="w-4 h-4 mr-1" />
                            Map
                        </Button>
                        <Button
                            variant={activeViews.has('grid') ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => toggleView('grid')}
                            className="h-8 px-3"
                        >
                            <Grid3X3 className="w-4 h-4 mr-1" />
                            List
                        </Button>
                        <Button
                            variant={isFiltersVisible ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setIsFiltersVisible(!isFiltersVisible)}
                            className="h-8 px-3"
                        >
                            <Filter className="w-4 h-4 mr-1" />
                            Filters
                        </Button>
                    </div>
                </div>
            </div>

            {/* Filters Panel */}
            {isFiltersVisible && (
                <div className="bg-card border-b border-border p-4">
                    <Card>
                        <CardHeader className="pb-4">
                            <div className="flex items-center justify-between">
                                <CardTitle className="text-lg">Search Filters</CardTitle>
                                <Button variant="ghost" size="sm" onClick={clearFilters}>
                                    Clear All
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Location and Date */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="location">Location</Label>
                                    <div className="relative">
                                        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="location"
                                            placeholder="City, address, or postal code"
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="dateFrom">From Date</Label>
                                    <Input
                                        id="dateFrom"
                                        type="date"
                                        value={dateFrom}
                                        onChange={(e) => setDateFrom(e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="dateTo">To Date</Label>
                                    <Input
                                        id="dateTo"
                                        type="date"
                                        value={dateTo}
                                        onChange={(e) => setDateTo(e.target.value)}
                                    />
                                </div>
                            </div>

                            {/* Property Type and Distance */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label>Property Type</Label>
                                    <Select value={propertyType} onValueChange={setPropertyType}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Any property type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="">Any property type</SelectItem>
                                            <SelectItem value="House">House</SelectItem>
                                            <SelectItem value="Condo">Condo</SelectItem>
                                            <SelectItem value="Townhouse">Townhouse</SelectItem>
                                            <SelectItem value="Apartment">Apartment</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="space-y-2">
                                    <Label>Distance: {distanceRange[0]} km</Label>
                                    <Slider
                                        value={distanceRange}
                                        onValueChange={setDistanceRange}
                                        max={50}
                                        min={1}
                                        step={1}
                                        className="w-full"
                                    />
                                </div>
                            </div>

                            {/* Price and Bedrooms */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label>Price Range: ${priceRange[0].toLocaleString()} - ${priceRange[1].toLocaleString()}</Label>
                                    <Slider
                                        value={priceRange}
                                        onValueChange={setPriceRange}
                                        max={2000000}
                                        min={0}
                                        step={25000}
                                        className="w-full"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label>Bedrooms: {bedroomRange[0]} - {bedroomRange[1]}</Label>
                                    <Slider
                                        value={bedroomRange}
                                        onValueChange={setBedroomRange}
                                        max={10}
                                        min={0}
                                        step={1}
                                        className="w-full"
                                    />
                                </div>
                            </div>

                            <div className="flex justify-end">
                                <Button onClick={handleSearch} className="px-8">
                                    <Search className="w-4 h-4 mr-2" />
                                    Search Open Houses
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Main Content */}
            <div className="flex flex-1 overflow-hidden">
                {/* Map Section */}
                {activeViews.has('map') && (
                    <div className={cn(
                        "h-full",
                        !activeViews.has('grid') ? "w-full" :
                            isFiltersVisible ? "w-2/3 border-r-2 border-border" : "w-1/2 border-r-2 border-border"
                    )}>
                        <OpenHouseMap
                            openHouses={openHouses}
                            onMapMove={handleMapMove}
                            initialCenter={mapCenter}
                            onOpenHouseClick={(openHouse) => console.log('Open house clicked:', openHouse)}
                        />
                    </div>
                )}

                {/* Grid Section */}
                {activeViews.has('grid') && (
                    <div className={cn(
                        "h-full flex flex-col",
                        !activeViews.has('map') ? "w-full" :
                            isFiltersVisible ? "w-1/3" : "w-1/2"
                    )}>
                        <div className="flex-1 overflow-y-auto p-4">
                            {loading ? (
                                <div className="flex items-center justify-center h-full">
                                    <div className="text-center space-y-2">
                                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                                        <p className="text-sm text-muted-foreground">Loading open houses...</p>
                                    </div>
                                </div>
                            ) : openHouses && openHouses.length > 0 ? (
                                <div className="space-y-4">
                                    {openHouses.map((openHouse) => (
                                        <OpenHouseCard
                                            key={openHouse.id}
                                            openHouse={openHouse}
                                            onRegister={(id) => console.log('Register for:', id)}
                                            onViewProperty={(propertyId) => router.push(`/properties/${propertyId}`)}
                                        />
                                    ))}
                                </div>
                            ) : (
                                <div className="flex items-center justify-center h-full">
                                    <div className="text-center space-y-4">
                                        <Calendar className="w-16 h-16 text-muted-foreground mx-auto" />
                                        <div>
                                            <h3 className="text-lg font-semibold">No open houses found</h3>
                                            <p className="text-muted-foreground">Try adjusting your search criteria</p>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
