'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { BuyerListingForm } from '@/components/properties/BuyerListingForm';
import { useUser } from '@clerk/nextjs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Home, ArrowLeft } from 'lucide-react';

export default function ListPropertyPage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (data: any) => {
    // Check authentication only when user tries to submit
    if (!user) {
      // Redirect to sign-in with return URL
      router.push('/sign-in?redirect=/list-property');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/buyer-listings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Property listed successfully:', result);

        // Redirect to properties page or dashboard
        router.push('/properties?success=listing-created');
      } else {
        const error = await response.json();
        console.error('Error listing property:', error);
        alert('Error listing property. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting listing:', error);
      alert('Error submitting listing. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  // Show loading only while Clerk is initializing
  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // No authentication gate - users can view the form without signing in
  // Authentication is only required when they try to submit (handled in handleSubmit)

  return (
    <div className="min-h-screen bg-background">
      <BuyerListingForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />

      {isSubmitting && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <p className="text-sm">Submitting your property listing...</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
