import Image from "next/image";
import config from "@/config";

const CTA = () => {
  return (
    <section className="relative hero overflow-hidden min-h-screen pt-32">
      <Image
        src="https://images.unsplash.com/photo-1571171637578-41bc2dd41cd2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=3540&q=80"
        alt="Background"
        className="object-cover w-full"
        fill
      />
      <div className="absolute top-0 right-0 bottom-0 left-0 hero-overlay dark:bg-gray-500 bg-white dark:bg-opacity-70 bg-opacity-50 text-center p-8 flex items-center justify-center">
        <div className="flex flex-col items-center max-w-xl p-8 md:p-0">
          <h2 className="font-bold text-3xl md:text-5xl tracking-tight mb-8 md:mb-12">
            Boost your app, launch, earn
          </h2>
          <p className="text-lg opacity-80 mb-12 md:mb-16">
            Don&apos;t waste time integrating APIs or designing a pricing
            section...
          </p>

          <button className="text-white font-semibold whitespace-nowrap text-sm px-20 py-4 rounded-lg transition-all duration-300 bg-primary">
            Get {config.appName}
          </button>
        </div>
      </div>
    </section>
  );
};

export default CTA;
