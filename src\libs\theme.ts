import { create } from 'zustand'
import { persist } from 'zustand/middleware'

type Theme = 'light' | 'dark' | 'system'

interface ThemeState {
  theme: Theme
  setTheme: (theme: Theme) => void
}

export const useTheme = create<ThemeState>()(
  persist(
    (set) => ({
      theme: 'light',
      setTheme: (theme) => set({ theme }),
    }),
    {
      name: 'theme-storage',
    }
  )
)

// Light green color palette
export const lightTheme = {
  primary: {
    DEFAULT: '#4ade80', // Light green
    foreground: '#ffffff',
  },
  secondary: {
    DEFAULT: '#86efac', // Lighter green
    foreground: '#064e3b', // Dark green text
  },
  accent: {
    DEFAULT: '#22c55e', // Medium green
    foreground: '#ffffff',
  },
  background: {
    DEFAULT: '#ffffff',
    secondary: '#f8fafc',
  },
  text: {
    DEFAULT: '#0f172a',
    secondary: '#475569',
  },
  border: '#e2e8f0',
}

// Dark theme with green accents
export const darkTheme = {
  primary: {
    DEFAULT: '#4ade80', // Light green
    foreground: '#ffffff',
  },
  secondary: {
    DEFAULT: '#86efac', // Lighter green
    foreground: '#064e3b', // Dark green text
  },
  accent: {
    DEFAULT: '#22c55e', // Medium green
    foreground: '#ffffff',
  },
  background: {
    DEFAULT: '#0f172a', // Dark blue-gray
    secondary: '#1e293b',
  },
  text: {
    DEFAULT: '#f8fafc',
    secondary: '#cbd5e1',
  },
  border: '#334155',
}

// Tailwind CSS configuration
export const tailwindConfig = {
  theme: {
    extend: {
      colors: {
        primary: lightTheme.primary,
        secondary: lightTheme.secondary,
        accent: lightTheme.accent,
        background: lightTheme.background,
        text: lightTheme.text,
        border: lightTheme.border,
      },
    },
  },
  darkMode: 'class',
} 