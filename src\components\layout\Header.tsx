'use client'

import * as React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useUser, useClerk } from '@clerk/nextjs'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useUserType } from '@/contexts/UserTypeContext'
import { LayoutGroup, motion } from "motion/react"
import { TextRotate } from "@/components/ui/text-rotate"
import { SignInButton } from '@clerk/nextjs'

import { Button } from '@/components/ui/button'
import { ThemeToggle } from '@/components/theme-toggle'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
    ChevronDown, LogOut, Settings, User, Home, Globe, Users, ShoppingCart, Sparkles
} from 'lucide-react'
import { HeaderNavigation } from '@/components/ca/FloatingMenuItems'

const COUNTRIES = [
    { code: 'US', name: 'United States', flag: '🇺🇸' },
    { code: 'CA', name: 'Canada', flag: '🇨🇦' },
];

export function Header() {
    const { isSignedIn, user } = useUser();
    const { signOut } = useClerk();
    const router = useRouter();
    const pathname = usePathname();
    const { userType, setUserType } = useUserType();
    const [currentCountry, setCurrentCountry] = useState<string | null>(null);
    const [showCountrySelector, setShowCountrySelector] = useState(true);

    useEffect(() => {
        // Determine current country from URL or localStorage
        if (pathname === '/us' || pathname.startsWith('/us/')) {
            setCurrentCountry('US');
        } else if (pathname === '/ca' || pathname.startsWith('/ca/')) {
            setCurrentCountry('CA');
        } else {
            const stored = localStorage.getItem('userCountry');
            if (stored && COUNTRIES.some(c => c.code === stored)) {
                setCurrentCountry(stored);
            } else {
                // Default to CA for localhost (as per RegionCheck)
                setCurrentCountry('CA');
            }
        }
        // Check if country is valid for showing selector
        setShowCountrySelector(localStorage.getItem('countryValid') === 'true');
    }, [pathname]);

    const handleSignOut = async () => {
        await signOut();
        router.push('/');
    };

    const handleCountryChange = (countryCode: string) => {
        localStorage.setItem('userCountry', countryCode);
        // Redirect directly to country-specific routes
        if (countryCode === 'CA') {
            router.push('/ca');
        } else if (countryCode === 'US') {
            router.push('/us');
        }
    };

    return (
        <header className="sticky top-0 z-50 w-full border-b border-border bg-background text-foreground">
            <div className="flex h-12 w-full items-center justify-between px-4">
                <Link href="/" className="flex items-center gap-0.5 h-8">
                    <Image
                        src="/logo.svg"
                        alt="SoNoBrokers"
                        width={32}
                        height={32}
                        className="h-8 w-8"
                    />
                    <div className="flex items-center h-8">
                        <LayoutGroup>
                            <motion.div className="flex items-center whitespace-pre text-xl font-bold h-8" layout>
                                <motion.span
                                    className="text-primary h-8 flex items-center"
                                    layout
                                    transition={{ type: "spring", damping: 30, stiffness: 400 }}
                                >
                                    SoNo{" "}
                                </motion.span>
                                <div
                                    className="px-2 py-1 rounded-md flex items-center justify-center h-8"
                                    style={{ backgroundColor: '#22c55e', color: '#22c55e' }}
                                >
                                    <TextRotate
                                        texts={[
                                            "Brokers!",
                                            "Brokers",
                                            "Brokers 🕶️🕶️🕶️",
                                        ]}
                                        mainClassName="text-black overflow-hidden flex items-center justify-center font-bold h-8"
                                        staggerFrom={"last"}
                                        initial={{ y: "100%" }}
                                        animate={{ y: 0 }}
                                        exit={{ y: "-120%" }}
                                        staggerDuration={0.025}
                                        splitLevelClassName="overflow-hidden pb-0.5"
                                        transition={{ type: "spring", damping: 30, stiffness: 400 }}
                                        rotationInterval={2000}
                                    />
                                </div>
                            </motion.div>
                        </LayoutGroup>
                    </div>
                </Link>

                <div className="flex items-center gap-0.5">
                    {/* Navigation Links - Integrated from FloatingMenuItems */}
                    {/* The HeaderNavigation component now handles these links based on userType */}
                    <HeaderNavigation userType={userType} isSignedIn={isSignedIn} /> {/* Added isSignedIn prop */}

                    <ThemeToggle />

                    {/* User Type Selector */}
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="h-8 px-1.5">
                                {userType === 'buyer' ? <ShoppingCart className="h-4 w-4 mr-1" /> : <Users className="h-4 w-4 mr-1" />}
                                <span className="text-sm text-gray-600 font-medium capitalize">{userType}</span>
                                <ChevronDown className="h-3 w-3 ml-1" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-28">
                            <DropdownMenuLabel className="text-gray-600 font-medium">View As</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                                onClick={() => setUserType('buyer')}
                                className={`cursor-pointer text-gray-600 font-medium ${userType === 'buyer' ? "bg-orange-50 text-orange-600" : ""}`}
                            >
                                <ShoppingCart className="h-4 w-4 mr-1" />
                                <span>Buyer</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                onClick={() => setUserType('seller')}
                                className={`cursor-pointer text-gray-600 font-medium ${userType === 'seller' ? "bg-green-50 text-green-600" : ""}`}
                            >
                                <Users className="h-4 w-4 mr-1" />
                                <span>Seller</span>
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>

                    {/* Development Region Tester Link */}
                    {process.env.NODE_ENV === 'development' && (
                        <Link href="/region-tester" className="flex items-center text-xs text-gray-600 font-medium">
                            🧪
                        </Link>
                    )}


                    {showCountrySelector && (
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline" size="sm" className="h-8 px-1.5">
                                    <Globe className="h-4 w-4 mr-1" />
                                    <span className="text-sm text-gray-600 font-medium">{currentCountry}</span>
                                    <ChevronDown className="h-3 w-3 ml-1" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-32">
                                <DropdownMenuLabel className="text-gray-600 font-medium">Region</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                {COUNTRIES.map((country) => (
                                    <DropdownMenuItem
                                        key={country.code}
                                        onClick={() => handleCountryChange(country.code)}
                                        className={`cursor-pointer text-gray-600 font-medium ${currentCountry === country.code ? "bg-orange-50 text-orange-600" : ""}`}
                                    >
                                        <span className="mr-1">{country.flag}</span>
                                        <span>{country.code}</span>
                                    </DropdownMenuItem>
                                ))}
                            </DropdownMenuContent>
                        </DropdownMenu>
                    )}

                    {isSignedIn ? (
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                                    <Image
                                        src={user?.imageUrl || '/assets/avatar.svg'}
                                        alt={user?.username || 'User'}
                                        fill
                                        className="rounded-full object-cover"
                                    />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-56">
                                <DropdownMenuLabel className="text-gray-600 font-medium">My Account</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem asChild>
                                    <Link href="/dashboard" className="flex items-center text-gray-600 font-medium">
                                        <Home className="mr-2 h-4 w-4" />
                                        <span>Dashboard</span>
                                    </Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem asChild>
                                    <Link href="/profile" className="flex items-center text-gray-600 font-medium">
                                        <User className="mr-2 h-4 w-4" />
                                        <span>Profile</span>
                                    </Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem asChild>
                                    <Link href="/settings" className="flex items-center text-gray-600 font-medium">
                                        <Settings className="mr-2 h-4 w-4" />
                                        <span>Settings</span>
                                    </Link>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                    onClick={handleSignOut}
                                    className="text-red-500 hover:text-red-600 cursor-pointer font-medium"
                                >
                                    <LogOut className="mr-2 h-4 w-4" />
                                    <span>Sign out</span>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    ) : (
                        <SignInButton mode="modal">
                            <Button variant="outline" size="sm" className="h-8 px-1.5 bg-gradient-to-r from-orange-50 to-green-50 border-orange-200 hover:border-orange-400" asChild>
                                <Link href="/sign-in" className="flex items-center gap-0.5 text-gray-600 font-medium">
                                    <Sparkles className="h-4 w-4 text-orange-500" />
                                    <span className="text-sm">Get Started</span>
                                </Link>
                            </Button>
                        </SignInButton>
                    )}
                </div >
            </div>
        </header>
    );
}
