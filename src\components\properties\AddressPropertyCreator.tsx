'use client';

import { useState } from 'react';
import { AddressValidationService } from '@/lib/address-validation';
import { AIPropertyService } from '@/lib/ai-services';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Loader2, MapPin, Sparkles, CheckCircle, AlertCircle } from 'lucide-react';

interface AddressPropertyCreatorProps {
    onPropertyCreated?: (property: any) => void;
    className?: string;
}

interface ValidationStep {
    address: string;
    isValidating: boolean;
    validationResult: any;
    propertyData: any;
    aiData: any;
}

export function AddressPropertyCreator({ onPropertyCreated, className = '' }: AddressPropertyCreatorProps) {
    const [step, setStep] = useState<'input' | 'validating' | 'preview' | 'creating'>('input');
    const [address, setAddress] = useState('36 Heron Pl, Hamilton, ON L9A 4Y8');
    const [validationResult, setValidationResult] = useState<any>(null);
    const [propertyData, setPropertyData] = useState<any>(null);
    const [aiData, setAiData] = useState<any>(null);
    const [error, setError] = useState<string>('');

    const validateAndGenerate = async () => {
        if (!address.trim()) {
            setError('Please enter an address');
            return;
        }

        setStep('validating');
        setError('');

        try {
            // Step 1: Validate address
            const validation = await AddressValidationService.validateAddress(address);

            if (!validation.isValid) {
                setError(validation.error || 'Invalid address');
                setStep('input');
                return;
            }

            setValidationResult(validation);

            // Step 2: Get property details
            const property = await AIPropertyService.createPropertyFromAddress(address);
            setPropertyData(property);

            // Step 3: Generate AI data
            const aiGeneratedData = await AIPropertyService.generateOpenHouseData(property);
            setAiData(aiGeneratedData);

            setStep('preview');
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred');
            setStep('input');
        }
    };

    const createProperty = async () => {
        setStep('creating');
        try {
            // In a real implementation, this would save to your database
            const finalProperty = {
                ...propertyData,
                aiGeneratedDescription: aiData.description,
                aiReports: aiData.reports,
                createdAt: new Date(),
                source: 'ai-generated'
            };

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));

            onPropertyCreated?.(finalProperty);

            // Reset form
            setStep('input');
            setAddress('');
            setValidationResult(null);
            setPropertyData(null);
            setAiData(null);
        } catch (err) {
            setError('Failed to create property');
            setStep('preview');
        }
    };

    if (step === 'validating') {
        return (
            <Card className={className}>
                <CardContent className="flex items-center justify-center py-12">
                    <div className="text-center space-y-4">
                        <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                        <div>
                            <h3 className="font-semibold">Processing Address</h3>
                            <p className="text-sm text-muted-foreground">
                                Validating address and generating AI property details...
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (step === 'creating') {
        return (
            <Card className={className}>
                <CardContent className="flex items-center justify-center py-12">
                    <div className="text-center space-y-4">
                        <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                        <div>
                            <h3 className="font-semibold">Creating Property</h3>
                            <p className="text-sm text-muted-foreground">
                                Saving property with AI-generated details...
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (step === 'preview' && propertyData && aiData) {
        return (
            <div className={`space-y-6 ${className}`}>
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <CheckCircle className="h-5 w-5 text-green-500" />
                            Address Validated & AI Data Generated
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{validationResult.formattedAddress}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Sparkles className="h-4 w-4 text-primary" />
                            <span className="text-sm text-muted-foreground">
                                AI generated {aiData.reports.length} comprehensive reports
                            </span>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Property Details Preview</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Label className="text-sm font-medium">Title</Label>
                                <p className="font-semibold">{propertyData.title}</p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium">Type</Label>
                                <p>{propertyData.propertyType}</p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium">Price</Label>
                                <p className="text-primary font-semibold">
                                    ${propertyData.price?.toLocaleString()}
                                </p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium">Size</Label>
                                <p>{propertyData.sqft} sq ft</p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium">Bedrooms</Label>
                                <p>{propertyData.bedrooms}</p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium">Bathrooms</Label>
                                <p>{propertyData.bathrooms}</p>
                            </div>
                        </div>

                        <div>
                            <Label className="text-sm font-medium">AI-Generated Description</Label>
                            <p className="text-sm mt-1 p-3 bg-muted rounded-lg leading-relaxed">
                                {aiData.description}
                            </p>
                        </div>

                        <div>
                            <Label className="text-sm font-medium">Features</Label>
                            <div className="flex flex-wrap gap-2 mt-1">
                                {propertyData.features?.map((feature: string) => (
                                    <Badge key={feature} variant="secondary">{feature}</Badge>
                                ))}
                            </div>
                        </div>

                        <div>
                            <Label className="text-sm font-medium">AI Reports Generated</Label>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-1">
                                {aiData.reports.map((report: any) => (
                                    <div key={report.id} className="flex items-center gap-2 p-2 border rounded text-sm">
                                        <Sparkles className="h-3 w-3 text-primary" />
                                        <span>{report.title.replace(' Report', '')}</span>
                                        {report.score && (
                                            <Badge variant="outline" className="text-xs">
                                                {report.score}/100
                                            </Badge>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <div className="flex gap-3">
                    <Button
                        variant="outline"
                        onClick={() => {
                            setStep('input');
                            setPropertyData(null);
                            setAiData(null);
                            setValidationResult(null);
                        }}
                    >
                        Start Over
                    </Button>
                    <Button onClick={createProperty} className="gap-2">
                        <Sparkles className="h-4 w-4" />
                        Create Property with AI Data
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <Card className={className}>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-primary" />
                    Create Property from Address
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                    Enter any address to validate it and generate AI-powered property details and neighborhood reports.
                </p>
            </CardHeader>
            <CardContent className="space-y-4">
                {error && (
                    <div className="flex items-center gap-2 p-3 rounded-lg border border-red-200 bg-red-50 text-red-700">
                        <AlertCircle className="h-4 w-4" />
                        <span className="text-sm">{error}</span>
                    </div>
                )}

                <div className="space-y-2">
                    <Label htmlFor="address">Property Address</Label>
                    <Input
                        id="address"
                        value={address}
                        onChange={(e) => setAddress(e.target.value)}
                        placeholder="Enter full address (e.g., 36 Heron Pl, Hamilton, ON L9A 4Y8)"
                        onKeyDown={(e) => e.key === 'Enter' && validateAndGenerate()}
                    />
                    <p className="text-xs text-muted-foreground">
                        Try: 36 Heron Pl, Hamilton, ON L9A 4Y8 (sample address with enhanced data)
                    </p>
                </div>

                <Button
                    onClick={validateAndGenerate}
                    disabled={!address.trim()}
                    className="w-full gap-2"
                >
                    <MapPin className="h-4 w-4" />
                    Validate Address & Generate AI Property
                </Button>

                <div className="text-center">
                    <p className="text-xs text-muted-foreground">
                        This will validate the address using Google Maps API and generate comprehensive
                        property details and neighborhood reports using AI.
                    </p>
                </div>
            </CardContent>
        </Card>
    );
}
