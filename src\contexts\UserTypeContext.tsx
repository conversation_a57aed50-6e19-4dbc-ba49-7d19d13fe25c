'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export type UserType = 'buyer' | 'seller'

interface UserTypeContextType {
  userType: UserType
  setUserType: (type: UserType) => void
}

const UserTypeContext = createContext<UserTypeContextType | undefined>(undefined)

interface UserTypeProviderProps {
  children: ReactNode
}

export function UserTypeProvider({ children }: UserTypeProviderProps) {
  const [userType, setUserTypeState] = useState<UserType>('buyer')

  // Load user type from localStorage on mount
  useEffect(() => {
    const stored = localStorage.getItem('userType')
    if (stored === 'buyer' || stored === 'seller') {
      setUserTypeState(stored)
    }
  }, [])

  // Save to localStorage when user type changes
  const setUserType = (type: UserType) => {
    setUserTypeState(type)
    localStorage.setItem('userType', type)
  }

  return (
    <UserTypeContext.Provider value={{ userType, setUserType }}>
      {children}
    </UserTypeContext.Provider>
  )
}

export function useUserType() {
  const context = useContext(UserTypeContext)
  if (context === undefined) {
    throw new Error('useUserType must be used within a UserTypeProvider')
  }
  return context
}
