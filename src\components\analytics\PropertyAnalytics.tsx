'use client'

import { Card, CardContent } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils"

interface Property {
  id: string
  title: string
  price: number
  status: 'active' | 'pending' | 'sold'
}

interface PropertyAnalyticsProps {
  analytics: {
    views: number
    saves: number
    inquiries: number
    averagePrice: number
    marketTrend: 'up' | 'down' | 'stable'
    daysOnMarket: number
    similarProperties: Property[]
  }
}

export function PropertyAnalytics({ analytics }: PropertyAnalyticsProps) {
  const { views, saves, inquiries, averagePrice, marketTrend, daysOnMarket, similarProperties } = analytics

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{views}</div>
            <p className="text-sm text-muted-foreground">Total Views</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{inquiries}</div>
            <p className="text-sm text-muted-foreground">Inquiries</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{daysOnMarket}</div>
            <p className="text-sm text-muted-foreground">Avg. Days on Market</p>
          </CardContent>
        </Card>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-2">Similar Properties</h3>
        <div className="space-y-2">
          {similarProperties.map(property => (
            <div key={property.id} className="flex justify-between items-center p-3 border rounded-md">
              <div>
                <p className="font-medium">{property.title}</p>
                <p className="text-sm text-muted-foreground capitalize">{property.status}</p>
              </div>
              <div className="font-medium">{formatCurrency(property.price)}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
} 
