'use client'

import * as React from 'react'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu'


interface HeaderNavigationProps {
  userType: 'buyer' | 'seller'
  onNavigate?: (page: string) => void
  isSignedIn?: boolean // Added isSignedIn prop
  currentCountry?: string // Added currentCountry prop
}

// ListItem component for consistent styling
const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a">
>(({ className, title, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-0.5 rounded-md p-2 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-xs leading-snug text-muted-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  )
})
ListItem.displayName = "ListItem"

// Seller menu items
const sellerServices = [
  {
    title: "AI Property Creator",
    href: "/properties/new/ai",
    description: "Create professional property listings with AI-powered tools and automation."
  },
  {
    title: "Photography Services",
    href: "/services/photography",
    description: "Professional real estate photography and virtual tours for your listings."
  },
  {
    title: "Staging Services",
    href: "/services/staging",
    description: "Home staging consultation to maximize your property's appeal."
  },
  {
    title: "Legal Services",
    href: "/services/legal",
    description: "Real estate lawyers and legal document preparation."
  },
  {
    title: "Marketing Services",
    href: "/services/marketing",
    description: "Digital marketing and listing promotion services."
  }
]

const sellerResources = [
  {
    title: "Market Analytics",
    href: "/resources/market-analytics",
    description: "Real-time market data and pricing insights for your area."
  },
  {
    title: "Pricing Calculator",
    href: "/resources/pricing-calculator",
    description: "Estimate your property value with our advanced calculator."
  },
  {
    title: "Seller Guide",
    href: "/resources/seller-guide",
    description: "Complete guide to selling your property successfully."
  },
  {
    title: "Commission Calculator",
    href: "/resources/commission-calculator",
    description: "Calculate potential savings with our commission structure."
  }
]

// Buyer menu items
const buyerServices = [
  {
    title: "Mortgage Calculator",
    href: "/services/mortgage-calculator",
    description: "Calculate monthly payments and affordability for your budget."
  },
  {
    title: "Home Inspection",
    href: "/services/inspection",
    description: "Professional home inspection services and scheduling."
  },
  {
    title: "Insurance Services",
    href: "/services/insurance",
    description: "Home insurance quotes and coverage options."
  },
  {
    title: "Moving Services",
    href: "/services/moving",
    description: "Trusted moving companies and relocation assistance."
  }
]

const buyerResources = [
  {
    title: "Buyer's Guide",
    href: "/resources/buyers-guide",
    description: "Complete guide to buying your first or next home."
  },
  {
    title: "Neighborhood Insights",
    href: "/resources/neighborhoods",
    description: "School ratings, amenities, and community information."
  },
  {
    title: "Market Trends",
    href: "/resources/market-trends",
    description: "Current market conditions and buying opportunities."
  },
  {
    title: "Financing Options",
    href: "/resources/financing",
    description: "Mortgage types, down payment assistance, and loan programs."
  }
]

// Header-specific navigation component with modern styling
export function HeaderNavigation({ userType, isSignedIn, currentCountry }: HeaderNavigationProps) { // Destructure isSignedIn and currentCountry
  const services = userType === 'seller' ? sellerServices : buyerServices
  const resources = userType === 'seller' ? sellerResources : buyerResources

  return (
    <NavigationMenu className="flex items-center">
      <NavigationMenuList className="flex items-center gap-[2px]">
        {userType === 'seller' ? (
          <>
            <NavigationMenuItem>
              <Link href="/list-property" className={cn(navigationMenuTriggerStyle(), "text-sm font-medium h-8 px-1.5 text-gray-600")}>
                List Property
              </Link>
            </NavigationMenuItem>
            {isSignedIn && ( // Conditionally render Dashboard link
              <NavigationMenuItem>
                <Link href={`/${currentCountry?.toLowerCase() || 'ca'}/dashboard`} className={cn(navigationMenuTriggerStyle(), "text-sm font-medium h-8 px-1.5 text-gray-600")}>
                  Dashboard
                </Link>
              </NavigationMenuItem>
            )}
            <NavigationMenuItem>
              <NavigationMenuTrigger className="text-sm font-medium h-8 px-1.5 text-gray-600">Services</NavigationMenuTrigger>
              <NavigationMenuContent
                className="bg-background border border-border shadow-lg"
                style={{
                  backgroundColor: 'white',
                  backdropFilter: 'none',
                  opacity: 1
                }}
              >
                <ul
                  className="grid gap-1.5 p-2 md:w-[380px] lg:w-[480px] lg:grid-cols-[.75fr_1fr] bg-background"
                  style={{
                    backgroundColor: 'white',
                    backdropFilter: 'none',
                    opacity: 1
                  }}
                >
                  <li className="row-span-3">
                    <NavigationMenuLink asChild>
                      <a
                        className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-r from-primary/10 to-secondary/10 p-6 no-underline outline-none focus:shadow-md"
                        href="/services"
                      >
                        <div className="mb-2 mt-4 text-lg font-medium">
                          Seller Services
                        </div>
                        <p className="text-sm leading-tight text-muted-foreground">
                          Professional services to help you sell your property faster and for more money.
                        </p>
                      </a>
                    </NavigationMenuLink>
                  </li>
                  {services.map((service) => (
                    <ListItem
                      key={service.title}
                      title={service.title}
                      href={service.href}
                    >
                      {service.description}
                    </ListItem>
                  ))}
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuTrigger className="text-sm font-medium h-8 px-1.5 text-gray-600">Resources</NavigationMenuTrigger>
              <NavigationMenuContent
                className="bg-background border border-border shadow-lg"
                style={{
                  backgroundColor: 'white',
                  backdropFilter: 'none',
                  opacity: 1
                }}
              >
                <ul
                  className="grid w-[380px] gap-1.5 p-2 md:w-[480px] md:grid-cols-2 lg:w-[580px] bg-background"
                  style={{
                    backgroundColor: 'white',
                    backdropFilter: 'none',
                    opacity: 1
                  }}
                >
                  {resources.map((resource) => (
                    <ListItem
                      key={resource.title}
                      title={resource.title}
                      href={resource.href}
                    >
                      {resource.description}
                    </ListItem>
                  ))}
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
          </>
        ) : (
          <>
            <NavigationMenuItem>
              <Link href="/properties" className={cn(navigationMenuTriggerStyle(), "text-sm font-medium h-8 px-1.5 text-gray-600")}>
                Property Search
              </Link>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <Link href="/properties/open-houses" className={cn(navigationMenuTriggerStyle(), "text-sm font-medium h-8 px-1.5 text-gray-600")}> {/* Adjusted style */}
                Open Houses
              </Link>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuTrigger className="text-sm font-medium h-8 px-1.5 text-gray-600">Services</NavigationMenuTrigger>
              <NavigationMenuContent
                className="bg-background border border-border shadow-lg"
                style={{
                  backgroundColor: 'white',
                  backdropFilter: 'none',
                  opacity: 1
                }}
              >
                <ul
                  className="grid gap-1.5 p-2 md:w-[380px] lg:w-[480px] lg:grid-cols-[.75fr_1fr] bg-background"
                  style={{
                    backgroundColor: 'white',
                    backdropFilter: 'none',
                    opacity: 1
                  }}
                >
                  <li className="row-span-3">
                    <NavigationMenuLink asChild>
                      <a
                        className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-r from-primary/10 to-secondary/10 p-6 no-underline outline-none focus:shadow-md"
                        href="/services"
                      >
                        <div className="mb-2 mt-4 text-lg font-medium">
                          Buyer Services
                        </div>
                        <p className="text-sm leading-tight text-muted-foreground">
                          Essential tools and services to help you find and purchase your perfect home.
                        </p>
                      </a>
                    </NavigationMenuLink>
                  </li>
                  {services.map((service) => (
                    <ListItem
                      key={service.title}
                      title={service.title}
                      href={service.href}
                    >
                      {service.description}
                    </ListItem>
                  ))}
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuTrigger className="text-sm font-medium h-8 px-1.5 text-gray-600">Resources</NavigationMenuTrigger>
              <NavigationMenuContent
                className="bg-background border border-border shadow-lg"
                style={{
                  backgroundColor: 'white',
                  backdropFilter: 'none',
                  opacity: 1
                }}
              >
                <ul
                  className="grid w-[380px] gap-1.5 p-2 md:w-[480px] md:grid-cols-2 lg:w-[580px] bg-background"
                  style={{
                    backgroundColor: 'white',
                    backdropFilter: 'none',
                    opacity: 1
                  }}
                >
                  {resources.map((resource) => (
                    <ListItem
                      key={resource.title}
                      title={resource.title}
                      href={resource.href}
                    >
                      {resource.description}
                    </ListItem>
                  ))}
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
          </>
        )}
        <NavigationMenuItem>
          <Link href="/about" className={cn(navigationMenuTriggerStyle(), "text-sm font-medium h-8 px-1 text-gray-600")}>
            About
          </Link>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  )
}
