import { Card } from "@/components/ui/card";

export function StepsSection() {
  const steps = [
    {
      number: 1,
      title: "Create Account",
      description: "Sign up as a buyer or seller"
    },
    {
      number: 2,
      title: "List or Browse",
      description: "Post your property or find your dream home"
    },
    {
      number: 3,
      title: "Connect",
      description: "Chat directly with interested parties"
    },
    {
      number: 4,
      title: "Close Deal",
      description: "Complete your transaction securely"
    }
  ];

  return (
    <section className="py-20 rounded-lg border-b border-[var(--border)] bg-card text-card-foreground shadow-sm">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {steps.map((step) => (
            <Card 
              key={step.number} 
              className="relative overflow-hidden border-[var(--border)] bg-card hover:shadow-md transition-shadow duration-200"
            >
              <div className="absolute top-0 left-0 w-full h-1 bg-primary" />
              <div className="p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-10 h-10 bg-primary/10 text-primary rounded-md flex items-center justify-center text-xl font-bold">
                    {step.number}
                  </div>
                  <h3 className="font-semibold text-lg">{step.title}</h3>
                </div>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
} 