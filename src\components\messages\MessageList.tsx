import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';

interface Message {
  id: string;
  content: string;
  created_at: string;
  sender: {
    id: string;
    full_name: string;
    avatar_url: string | null;
  };
  property?: {
    id: string;
    title: string;
    images: { url: string }[];
  };
}

interface MessageListProps {
  messages: Message[];
  onMessageClick: (message: Message) => void;
}

export function MessageList({ messages, onMessageClick }: MessageListProps) {
  return (
    <div className="space-y-4">
      {messages.map((message) => (
        <Card
          key={message.id}
          className="cursor-pointer hover:bg-accent/50 transition-colors"
          onClick={() => onMessageClick(message)}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center space-x-4">
              {message.sender.avatar_url && (
                <div className="relative w-10 h-10 rounded-full overflow-hidden">
                  <img
                    src={message.sender.avatar_url}
                    alt={message.sender.full_name}
                    className="object-cover"
                  />
                </div>
              )}
              <div>
                <CardTitle className="text-base">{message.sender.full_name}</CardTitle>
                <p className="text-sm text-muted-foreground">
                  {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                </p>
              </div>
            </div>
            {message.property && (
              <Badge variant="secondary" className="ml-auto">
                {message.property.title}
              </Badge>
            )}
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground line-clamp-2">
              {message.content}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}