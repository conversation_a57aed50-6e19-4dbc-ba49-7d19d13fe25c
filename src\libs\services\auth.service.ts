import { prisma } from '../prisma';
import { User } from '@prisma/client';

export class AuthService {
  static async trackLogin(userId: string): Promise<void> {
    await prisma.user.update({
      where: { id: userId },
      data: {
        lastLoginAt: new Date(),
        loggedIn: true,
      },
    });
  }

  static async trackLogout(userId: string): Promise<void> {
    await prisma.user.update({
      where: { id: userId },
      data: {
        loggedIn: false,
      },
    });
  }

  static async getUserLoginStatus(userId: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { loggedIn: true },
    });
    return user?.loggedIn ?? false;
  }

  static async getLastLoginTime(userId: string): Promise<Date | null> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { lastLoginAt: true },
    });
    return user?.lastLoginAt ?? null;
  }
} 