'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { MessageSquare } from 'lucide-react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';

interface ContactSellerButtonProps {
    sellerId: string;
    propertyId: string;
}

export function ContactSellerButton({ sellerId, propertyId }: ContactSellerButtonProps) {
    const { isSignedIn } = useUser();
    const router = useRouter();

    const handleContactSeller = () => {
        if (!isSignedIn) {
            router.push(`/sign-in?redirect=/properties/${propertyId}`);
            return;
        }
        // TODO: Implement actual contact seller functionality (e.g., open chat, show contact form)
        console.log(`Contacting seller ${sellerId} for property ${propertyId}`);
        alert(`Contacting seller ${sellerId} for property ${propertyId} (feature in development)`);
    };

    return (
        <Button className="w-full mt-4" onClick={handleContactSeller}>
            <MessageSquare className="mr-2 h-4 w-4" /> Contact Seller
        </Button>
    );
}
