'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'

const SUPPORTED_REGIONS = ['US', 'CA']

/**
 * RegionCheck Component
 *
 * Handles region detection and routing based on user location.
 *
 * Testing Options (via browser console):
 * - Test Canada: localStorage.setItem('testCountry', 'CA')
 * - Test US: localStorage.setItem('testCountry', 'US')
 * - Test unsupported region: localStorage.setItem('testCountry', 'FR')
 * - Clear test: localStorage.removeItem('testCountry')
 * - Clear all: localStorage.removeItem('testCountry'); localStorage.removeItem('userCountry')
 *
 * Localhost behavior: Defaults to Canada (CA)
 */

interface RegionData {
  country: string
  countryName: string
  city: string
  region: string
  timezone: string
}

export function RegionCheck({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const pathname = usePathname()
  const [isChecking, setIsChecking] = useState(true)
  const [userCountry, setUserCountry] = useState<string | null>(null)

  useEffect(() => {
    // Skip region check if already on a country path or unsupported page
    if (pathname.startsWith('/country/') || pathname === '/unsupported-region') {
      setIsChecking(false)
      return
    }

    // Skip check for certain paths
    const excludedPaths = ['/login', '/signup', '/reset-password', '/api', '/sign-in', '/sign-up', '/dashboard', '/onboarding']
    if (excludedPaths.some(path => pathname.startsWith(path))) {
      setIsChecking(false)
      return
    }

    const checkRegion = async () => {
      try {
        // Check for testing override in localStorage
        const testCountry = localStorage.getItem('testCountry')
        if (testCountry) {
          console.log(`🧪 Testing mode: Using country override "${testCountry}"`)
          const country = testCountry.toUpperCase()
          localStorage.setItem('userCountry', country)
          setUserCountry(country)

          if (SUPPORTED_REGIONS.includes(country)) {
            localStorage.setItem('countryValid', 'true')
            setIsChecking(false)
            return
          } else {
            localStorage.setItem('countryValid', 'false')
            router.push(`/unsupported-region?country=${country}`)
            setIsChecking(false)
            return
          }
        }

        // Check if running on localhost
        const isLocalhost = typeof window !== 'undefined' &&
          (window.location.hostname === 'localhost' ||
            window.location.hostname === '127.0.0.1' ||
            window.location.hostname === '0.0.0.0')

        if (isLocalhost) {
          console.log('🏠 Localhost detected: Defaulting to Canada')
          const country = 'CA'
          localStorage.setItem('userCountry', country)
          localStorage.setItem('countryValid', 'true')
          setUserCountry(country)
          setIsChecking(false)
          return
        }

        // First check localStorage for cached country
        const storedCountry = localStorage.getItem('userCountry')

        if (storedCountry) {
          setUserCountry(storedCountry)

          if (SUPPORTED_REGIONS.includes(storedCountry)) {
            localStorage.setItem('countryValid', 'true')
            setIsChecking(false)
            return
          } else {
            localStorage.setItem('countryValid', 'false')
            router.push(`/unsupported-region?country=${storedCountry}`)
            setIsChecking(false)
            return
          }
        }

        // If no stored country, fetch from API
        const response = await fetch('/api/geo?ip=*********')
        if (!response.ok) {
          throw new Error('Failed to fetch region data')
        }

        const data: RegionData = await response.json()
        const country = data.country

        // Validate country data
        if (!country || country === 'undefined' || country === 'null') {
          throw new Error('Invalid country data received')
        }

        // Store the country in localStorage
        localStorage.setItem('userCountry', country)
        setUserCountry(country)

        // Handle routing based on country
        if (SUPPORTED_REGIONS.includes(country)) {
          localStorage.setItem('countryValid', 'true')
          setIsChecking(false)
          return
        } else {
          localStorage.setItem('countryValid', 'false')
          router.push(`/unsupported-region?country=${country}`)
        }
      } catch (error) {
        console.error('Error checking region:', error)
        // Default to Canada on error (changed from US)
        localStorage.setItem('userCountry', 'CA')
        localStorage.setItem('countryValid', 'true')
        setUserCountry('CA')
        setIsChecking(false)
      }
    }

    // Use a short timeout to avoid immediate redirect
    const timer = setTimeout(() => {
      checkRegion()
    }, 100)

    return () => clearTimeout(timer)
  }, [router, pathname])

  if (isChecking) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  }

  return <>{children}</>
}
