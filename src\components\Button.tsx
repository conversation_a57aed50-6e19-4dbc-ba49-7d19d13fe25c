import React from "react";
import { But<PERSON> as ShadeCnButton } from "@/components/ui/button";

const Button = ({
  text,
  onClick,
  disabled,
  isLoading,
}: {
  text: string;
  onClick?: () => void;
  disabled?: boolean;
  isLoading?: boolean;
}) => {
  return (
    <ShadeCnButton
      onClick={onClick}
      disabled={disabled || false}
      className=""
    >
      {isLoading ? (
        <span className="loading loading-spinner loading-xs"></span>
      ) : (
        text
      )}
    </ShadeCnButton>
  );
};

export default Button;
