import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs'
import { Viewport } from 'next'
import { Inter } from 'next/font/google'
import { ReactNode, Suspense } from 'react'
// import { headers } from 'next/headers' // Removed unused import to fix Next.js 15 compatibility
import { NavigationEvents } from '@/components/NavigationEvents'
import { RegionCheck } from '@/components/RegionCheck'
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'
import type { Metadata } from "next";
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/toaster'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { UserTypeProvider } from '@/contexts/UserTypeContext'

import cn from 'classnames'
import '@/assets/styles/globals.css'

const inter = Inter({ subsets: ['latin'] })

export const viewport: Viewport = {
	themeColor: '#000000',
	width: 'device-width',
	initialScale: 1,
}

export const metadata: Metadata = {
	title: "SoNo Brokers",
	description: "Find your perfect home with SoNo Brokers",
	icons: {
		icon: "/favicon.ico",
	},
}

export default async function RootLayout({ children }: { children: ReactNode }) {
	return (
		<ClerkProvider
			appearance={{
				baseTheme: undefined,
				elements: {
					formButtonPrimary: 'bg-primary text-primary-foreground hover:bg-primary/90',
					footerActionLink: 'text-primary hover:text-primary/90',
				},
			}}
		>
			<html lang='en' suppressHydrationWarning>
				<body className={cn('min-h-screen bg-background font-sans antialiased', inter.className)}>
					<ThemeProvider
						attribute="class"
						defaultTheme="system"
						enableSystem
						disableTransitionOnChange
					>
						<UserTypeProvider>
							<RegionCheck>
								<div className="relative min-h-screen flex flex-col px-4">
									<Header />
									<main className="flex-1">
										{children}
									</main>
									<Footer />
									<Toaster />
									<Suspense fallback={null}>
										<NavigationEvents />
									</Suspense>
								</div>
							</RegionCheck>
						</UserTypeProvider>
					</ThemeProvider>
					<Analytics />
					<SpeedInsights />
				</body>
			</html>
		</ClerkProvider >
	)
}
