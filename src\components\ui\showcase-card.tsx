'use client'
import React, { useState } from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { cn } from "@/lib/utils" // Assuming you have this utility
import { Heart, ArrowUpRight, MapPin, Bed, Bath, Square, Eye, Calendar } from 'lucide-react' // Icons
import { Button } from "@/components/ui/button"

interface ShowcaseCardProps {
    propertyId: string;
    imageUrl?: string;
    price: string;
    title: string;
    location: string;
    bedrooms: number;
    bathrooms: number;
    sqft: number;
    views: number;
    daysListed: number;
    isLiked: boolean;
    onLikeClick: (propertyId: string) => void;
    onDetailsLinkClick: (propertyId: string) => void;
    onClick?: (propertyId: string) => void;
    className?: string;
}

export const ShowcaseCard = ({
    propertyId,
    imageUrl = "https://images.unsplash.com/photo-1544077960-604201fe74bc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1651&q=80",
    price,
    title,
    location,
    bedrooms,
    bathrooms,
    sqft,
    views,
    daysListed,
    isLiked,
    onLikeClick,
    onDetailsLinkClick,
    onClick,
    className,
}: ShowcaseCardProps) => {
    const [isHovered, setIsHovered] = useState(false)

    const handleCardClick = () => {
        if (onClick) {
            onClick(propertyId);
        }
    };

    const handleLikeButtonClick = (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent card click event
        onLikeClick(propertyId);
    };

    const handleDetailsLinkButtonClick = (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent card click event
        onDetailsLinkClick(propertyId);
    };

    return (
        <motion.div
            className={cn(
                'bg-background rounded-2xl shadow-lg border flex flex-col h-full min-w-[260px] max-w-[340px] overflow-hidden',
                onClick ? 'cursor-pointer' : '',
                className
            )}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            whileHover={{ scale: 1.01 }}
            onHoverStart={() => setIsHovered(true)}
            onHoverEnd={() => setIsHovered(false)}
            onClick={handleCardClick}
        >
            <div className="relative h-40 bg-gray-100 rounded-t-2xl overflow-hidden">
                <Image
                    src={imageUrl}
                    alt={title}
                    className="object-cover w-full h-full"
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10" />
                <div className="absolute top-3 right-3 z-20 flex gap-2">
                    <Button variant="ghost" size="sm" className="text-white hover:text-red-500 hover:bg-white/20 bg-black/40" onClick={handleLikeButtonClick}>
                        <Heart className={cn("h-5 w-5", isLiked ? "fill-red-500 text-red-500" : "")}/>
                    </Button>
                    <Button variant="ghost" size="sm" className="text-white hover:text-primary hover:bg-white/20 bg-black/40" onClick={handleDetailsLinkButtonClick}>
                        <ArrowUpRight className="h-5 w-5" />
                    </Button>
                </div>
                <div className="absolute bottom-3 left-3 z-20 text-white">
                    <div className="flex items-center gap-4 text-xs">
                        <div className="flex items-center gap-1"><Eye className="h-3 w-3" /><span>{views}</span></div>
                        <div className="flex items-center gap-1"><Calendar className="h-3 w-3" /><span>{daysListed}d ago</span></div>
                    </div>
                </div>
            </div>
            <div className="p-4 flex-1 flex flex-col justify-between">
                <div>
                    <h3 className="text-lg font-semibold text-foreground mb-1 group-hover:text-primary transition-colors truncate">{title}</h3>
                    <div className="flex items-center text-muted-foreground mb-1"><MapPin className="h-4 w-4 mr-1" /><span className="text-sm truncate">{location}</span></div>
                    <p className="text-xl font-bold text-foreground">{price}</p>
                </div>
                <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
                    <div className="flex items-center gap-1"><Bed className="h-4 w-4" /><span>{bedrooms} bed</span></div>
                    <div className="flex items-center gap-1"><Bath className="h-4 w-4" /><span>{bathrooms} bath</span></div>
                    <div className="flex items-center gap-1"><Square className="h-4 w-4" /><span>{sqft} sqft</span></div>
                </div>
            </div>
        </motion.div>
    )
}
