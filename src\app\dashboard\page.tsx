import { Dashboard } from '@/components/dashboard/Dashboard';
import { redirect } from 'next/navigation';
import { currentUser } from '@clerk/nextjs/server';
import { prisma } from '@/libs/prisma';

async function getUserWithRole() {
	const clerkUser = await currentUser();
	if (!clerkUser) return null;
	// Fetch user from DB by email
	const userEmail = clerkUser.emailAddresses[0].emailAddress;
	const dbUser = await prisma.user.findUnique({
		where: { email: userEmail },
		select: { id: true, userType: true, fullName: true },
	});
	if (!dbUser) return null;
	return {
		id: dbUser.id,
		role: dbUser.userType as 'buyer' | 'seller' | 'admin' | 'product' | string,
		name: dbUser.fullName,
		email: userEmail,
	};
}

export default async function DashboardPage() {
	const user = await getUserWithRole();
	if (!user || !['buyer', 'seller', 'admin', 'product'].includes(user.role)) {
		redirect('/onboarding');
	}
	// Pass the user object directly
	return <Dashboard user={user} />;
}
