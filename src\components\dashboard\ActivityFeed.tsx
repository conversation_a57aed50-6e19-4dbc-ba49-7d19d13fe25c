import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { formatDistanceToNow } from 'date-fns';

interface Activity {
  id: string;
  type: 'property_view' | 'property_save' | 'message_sent' | 'inquiry_sent' | 'profile_update';
  description: string;
  timestamp: string;
  metadata?: {
    propertyId?: string;
    propertyTitle?: string;
    recipientId?: string;
    recipientName?: string;
  };
}

interface ActivityFeedProps {
  activities: Activity[];
}

export function ActivityFeed({ activities }: ActivityFeedProps) {
  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'property_view':
        return '👁️';
      case 'property_save':
        return '🔖';
      case 'message_sent':
        return '💬';
      case 'inquiry_sent':
        return '❓';
      case 'profile_update':
        return '👤';
      default:
        return '•';
    }
  };

  const getActivityColor = (type: Activity['type']) => {
    switch (type) {
      case 'property_view':
        return 'text-green-500';
      case 'property_save':
        return 'text-blue-500';
      case 'message_sent':
        return 'text-purple-500';
      case 'inquiry_sent':
        return 'text-yellow-500';
      case 'profile_update':
        return 'text-orange-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        {activities.length === 0 ? (
          <p className="text-center text-muted-foreground py-4">
            No recent activity
          </p>
        ) : (
          <div className="space-y-4">
            {activities.map((activity) => (
              <div
                key={activity.id}
                className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50"
              >
                <span
                  className={`text-lg ${getActivityColor(activity.type)}`}
                >
                  {getActivityIcon(activity.type)}
                </span>
                <div className="flex-1 min-w-0">
                  <p className="text-sm">
                    {activity.description}
                    {activity.metadata?.propertyTitle && (
                      <span className="font-medium">
                        {' '}
                        - {activity.metadata.propertyTitle}
                      </span>
                    )}
                    {activity.metadata?.recipientName && (
                      <span className="font-medium">
                        {' '}
                        - {activity.metadata.recipientName}
                      </span>
                    )}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    {formatDistanceToNow(new Date(activity.timestamp), {
                      addSuffix: true,
                    })}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 