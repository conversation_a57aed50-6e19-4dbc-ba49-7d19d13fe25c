'use client'

import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { cn } from '@/lib/utils'

interface SkeletonSectionProps {
  variant?: 'hero' | 'services' | 'how-it-works' | 'cta'
  className?: string
}

export function SkeletonSection({ variant = 'hero', className }: SkeletonSectionProps) {
  const renderHeroSkeleton = () => (
    <section className={cn("py-20 lg:py-32 bg-gradient-to-br from-background via-accent/5 to-background", className)}>
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content Side */}
          <div className="space-y-8">
            <div className="space-y-4">
              <Skeleton className="h-6 w-20 bg-accent/20" />
              <Skeleton className="h-16 w-full bg-gradient-to-r from-accent/10 to-accent/5" />
              <Skeleton className="h-6 w-3/4 bg-accent/20" />
              <Skeleton className="h-20 w-full bg-gradient-to-r from-muted/50 to-muted/30" />
            </div>

            <div className="grid grid-cols-2 gap-4">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i} className="bg-background/50 backdrop-blur border-white/20">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-8 w-8 rounded-full bg-accent/20" />
                      <Skeleton className="h-4 flex-1 bg-muted/50" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="flex gap-4">
              <Skeleton className="h-12 w-40 bg-gradient-to-r from-accent/20 to-accent/10" />
              <Skeleton className="h-12 w-32 bg-muted/30" />
            </div>
          </div>

          {/* Visual Side */}
          <Card className="bg-background/50 backdrop-blur border-white/20 shadow-xl">
            <CardContent className="p-8">
              <div className="space-y-6">
                <div className="text-center space-y-4">
                  <Skeleton className="h-8 w-3/4 mx-auto bg-accent/20" />
                  <Skeleton className="h-4 w-full bg-muted/50" />
                </div>

                <Skeleton className="h-4 w-full bg-accent/10" />

                <div className="grid grid-cols-2 gap-4">
                  <Skeleton className="h-20 bg-muted/30" />
                  <Skeleton className="h-20 bg-muted/30" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )

  const renderServicesSkeleton = () => (
    <section className={cn("py-20 bg-gradient-to-br from-background/50 via-accent/5 to-background/50", className)}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-16 space-y-6">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Skeleton className="h-12 w-12 rounded-2xl bg-gradient-to-br from-blue-500/20 to-indigo-500/20" />
            <Skeleton className="h-6 w-40 bg-background/60 backdrop-blur border border-blue-200/50 rounded-full" />
          </div>
          <Skeleton className="h-12 w-80 mx-auto bg-gradient-to-r from-foreground/10 to-accent/10 rounded-lg" />
          <Skeleton className="h-6 w-96 mx-auto bg-muted/30 rounded-lg" />
        </div>

        {/* Service Categories Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16 max-w-5xl mx-auto">
          {[
            { gradient: 'from-emerald-500/10 to-teal-500/10', border: 'border-emerald-200/30' },
            { gradient: 'from-blue-500/10 to-indigo-500/10', border: 'border-blue-200/30' },
            { gradient: 'from-purple-500/10 to-violet-500/10', border: 'border-purple-200/30' }
          ].map((style, i) => (
            <Card key={i} className={cn(
              "bg-background/60 backdrop-blur-xl border shadow-lg hover:shadow-xl transition-all duration-300",
              style.border
            )}>
              <CardContent className="p-8 text-center space-y-6">
                <div className={cn(
                  "w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br flex items-center justify-center",
                  style.gradient
                )}>
                  <Skeleton className="h-8 w-8 bg-white/20 rounded-lg" />
                </div>
                <div className="space-y-3">
                  <Skeleton className="h-6 w-40 mx-auto bg-accent/20 rounded-lg" />
                  <Skeleton className="h-16 w-full bg-muted/20 rounded-lg" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Featured Providers */}
        <div className="space-y-8 max-w-5xl mx-auto">
          <div className="text-center">
            <Skeleton className="h-8 w-64 mx-auto bg-accent/20 rounded-lg mb-4" />
            <Skeleton className="h-4 w-80 mx-auto bg-muted/30 rounded-lg" />
          </div>

          {[1, 2].map((i) => (
            <Card key={i} className="bg-background/60 backdrop-blur-xl border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-8">
                <div className="flex flex-col lg:flex-row gap-8">
                  <div className="flex-1 space-y-6">
                    <div className="flex items-start gap-6">
                      <Skeleton className="w-20 h-20 rounded-2xl bg-gradient-to-br from-accent/20 to-accent/10" />
                      <div className="flex-1 space-y-3">
                        <Skeleton className="h-7 w-56 bg-accent/20 rounded-lg" />
                        <Skeleton className="h-5 w-40 bg-accent/15 rounded-lg" />
                        <Skeleton className="h-4 w-72 bg-muted/30 rounded-lg" />
                        <div className="flex gap-3 mt-4">
                          {[1, 2, 3].map((j) => (
                            <Skeleton key={j} className="h-7 w-24 bg-muted/20 rounded-full" />
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="lg:w-56 space-y-4">
                    <div className="text-center lg:text-right space-y-2">
                      <Skeleton className="h-8 w-28 bg-accent/20 rounded-lg mx-auto lg:ml-auto" />
                      <Skeleton className="h-4 w-20 bg-muted/30 rounded-lg mx-auto lg:ml-auto" />
                    </div>
                    <div className="space-y-3">
                      <Skeleton className="h-12 w-full bg-gradient-to-r from-accent/20 to-accent/10 rounded-lg" />
                      <Skeleton className="h-12 w-full bg-muted/20 rounded-lg" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )

  const renderHowItWorksSkeleton = () => (
    <section className={cn("py-16 bg-background", className)}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 space-y-4">
          <Skeleton className="h-10 w-48 mx-auto bg-accent/20" />
          <Skeleton className="h-6 w-80 mx-auto bg-muted/50" />
        </div>

        <div className="max-w-6xl mx-auto">
          {/* Desktop Layout */}
          <div className="hidden md:block">
            <div className="relative">
              <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-accent/30 transform -translate-y-1/2 z-0"></div>

              <div className="grid grid-cols-5 gap-8 relative z-10">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="text-center">
                    <Card className="bg-background/80 backdrop-blur border-white/20 shadow-lg mb-6">
                      <CardContent className="p-6 space-y-4">
                        <div className="relative">
                          <Skeleton className="w-16 h-16 rounded-full mx-auto bg-accent/20" />
                          <Skeleton className="absolute -top-2 -right-2 w-6 h-6 rounded bg-accent/30" />
                        </div>
                        <Skeleton className="h-6 w-24 mx-auto bg-accent/20" />
                        <Skeleton className="h-16 w-full bg-muted/30" />
                        <Skeleton className="h-6 w-20 mx-auto bg-muted/20" />
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="md:hidden space-y-6">
            {[1, 2, 3, 4, 5].map((i) => (
              <Card key={i} className="bg-background/80 backdrop-blur border-white/20 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="relative flex-shrink-0">
                      <Skeleton className="w-12 h-12 rounded-full bg-accent/20" />
                      <Skeleton className="absolute -top-1 -right-1 w-5 h-5 rounded bg-accent/30" />
                    </div>
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-6 w-32 bg-accent/20" />
                      <Skeleton className="h-12 w-full bg-muted/30" />
                      <Skeleton className="h-5 w-24 bg-muted/20" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Benefits Summary */}
        <div className="mt-16 text-center">
          <Card className="max-w-2xl mx-auto bg-accent/10 backdrop-blur border-accent/20">
            <CardContent className="p-8 space-y-6">
              <Skeleton className="h-8 w-48 mx-auto bg-accent/20" />
              <div className="grid md:grid-cols-3 gap-6">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="text-center space-y-2">
                    <Skeleton className="h-8 w-8 mx-auto bg-accent/20" />
                    <Skeleton className="h-5 w-24 mx-auto bg-accent/20" />
                    <Skeleton className="h-4 w-32 mx-auto bg-muted/30" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )

  const renderCtaSkeleton = () => (
    <section className={cn("py-20 bg-gradient-to-br from-accent/10 via-background to-accent/5", className)}>
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <Card className="bg-background/80 backdrop-blur border-white/20 shadow-2xl overflow-hidden">
            <CardContent className="p-0">
              <div className="grid lg:grid-cols-2 gap-0">
                {/* Content Side */}
                <div className="p-8 lg:p-12 space-y-8">
                  <div className="space-y-4">
                    <Skeleton className="h-6 w-20 bg-accent/20" />
                    <Skeleton className="h-12 w-full bg-gradient-to-r from-accent/20 to-accent/10" />
                    <Skeleton className="h-6 w-3/4 bg-accent/20" />
                    <Skeleton className="h-16 w-full bg-muted/30" />
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    {[1, 2, 3].map((i) => (
                      <Card key={i} className="p-4 bg-accent/10 border-accent/20">
                        <div className="text-center space-y-2">
                          <Skeleton className="h-6 w-6 mx-auto bg-accent/20" />
                          <Skeleton className="h-5 w-12 mx-auto bg-accent/20" />
                          <Skeleton className="h-3 w-16 mx-auto bg-muted/30" />
                        </div>
                      </Card>
                    ))}
                  </div>

                  <div className="space-y-4">
                    <Skeleton className="h-14 w-full bg-gradient-to-r from-accent/20 to-accent/10" />
                    <Skeleton className="h-14 w-full bg-muted/30" />
                  </div>

                  <div className="pt-4 border-t border-border">
                    <div className="flex justify-center gap-6">
                      {[1, 2, 3].map((i) => (
                        <Skeleton key={i} className="h-4 w-20 bg-muted/30" />
                      ))}
                    </div>
                  </div>
                </div>

                {/* Visual Side */}
                <div className="bg-gradient-to-br from-accent/20 to-accent/10 p-8 lg:p-12 flex items-center justify-center">
                  <div className="text-center space-y-6">
                    <Skeleton className="w-32 h-32 rounded-full mx-auto bg-accent/20" />
                    <div className="space-y-2">
                      <Skeleton className="h-8 w-20 mx-auto bg-accent/20" />
                      <Skeleton className="h-4 w-32 mx-auto bg-muted/30" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-center gap-1">
                        {[1, 2, 3, 4, 5].map((i) => (
                          <Skeleton key={i} className="h-5 w-5 bg-accent/20" />
                        ))}
                      </div>
                      <Skeleton className="h-4 w-40 mx-auto bg-muted/30" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )

  switch (variant) {
    case 'hero':
      return renderHeroSkeleton()
    case 'services':
      return renderServicesSkeleton()
    case 'how-it-works':
      return renderHowItWorksSkeleton()
    case 'cta':
      return renderCtaSkeleton()
    default:
      return renderHeroSkeleton()
  }
}
