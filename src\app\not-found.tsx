import { getSEOTags } from '@/libs/seo'
import config from '@/config'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Home } from 'lucide-react'

export const metadata = getSEOTags({
  title: `Page Not Found | ${config.appName}`,
  description: `The page you're looking for doesn't exist or has been moved.`,
  noIndex: true,
})

export default function NotFound() {
  return (
    <div className="container flex flex-col items-center justify-center min-h-[70vh] px-4 py-16 text-center">
      <div className="w-full max-w-md mx-auto">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 800 600"
          className="w-full h-64 mb-8"
        >
          <path
            fill="#f2f2f2"
            d="M951.957 384.116c0 10.949-158.21 124.523-353.349 124.523S245.26 395.065 245.26 384.116s158.209-19.825 353.348-19.825 353.349 8.876 353.349 19.825z"
          />
          <ellipse
            cx={554.999}
            cy={563.5}
            rx={223.5}
            ry={20.5}
            fill="#3f3d56"
          />
          <path
            fill="#3f3d56"
            d="M698.957 398.616c0 10.949-158.21 124.523-353.349 124.523S-7.74 409.565-7.74 398.616s158.209-19.825 353.348-19.825 353.349 8.876 353.349 19.825z"
          />
          <path
            fill="#6c63ff"
            d="M432.497 512.456a3.78 3.78 0 0 1-2.74-6.552l.26-1.03-.103-.247c-3.48-8.297-25.685 14.834-26.645 22.632a30.029 30.029 0 0 0 .527 10.328 120.392 120.392 0 0 1-10.952-50.003 116.202 116.202 0 0 1 .72-12.963q.598-5.293 1.658-10.51a121.787 121.787 0 0 1 24.151-51.617c6.874.383 12.898-.664 13.48-13.986.103-2.37 1.86-4.421 2.248-6.756a30.72 30.72 0 0 1-1.98.183l-.623.032-.077.004a3.745 3.745 0 0 1-3.076-6.101l.85-1.046c.43-.538.872-1.065 1.302-1.603a1.865 1.865 0 0 0 .14-.161c.495-.613.99-1.216 1.485-1.829a10.83 10.83 0 0 0-3.55-3.432c-4.96-2.904-11.802-.893-15.384 3.593-3.593 4.486-4.271 10.78-3.023 16.385a43.398 43.398 0 0 0 6.003 13.383c-.27.344-.549.677-.818 1.022a122.574 122.574 0 0 0-12.793 20.268c1.016-7.939-11.412-36.608-16.218-42.68-5.773-7.295-17.611-4.112-18.628 5.135l-.03.268q1.072.604 2.097 1.283a5.127 5.127 0 0 1-2.067 9.33l-.104.016c-9.556 13.644 21.077 49.155 28.745 41.182a125.11 125.11 0 0 0-6.735 31.692 118.664 118.664 0 0 0 .086 19.16l-.032-.226c-1.704-13.882-30.931-34.522-39.466-32.803-4.917.99-9.76.765-9.013 5.725l.036.237a34.442 34.442 0 0 1 3.862 1.861q1.07.605 2.096 1.283a5.127 5.127 0 0 1-2.067 9.33l-.104.016-.215.033c-4.35 14.966 27.907 39.12 47.517 31.434h.011a125.075 125.075 0 0 0 8.402 24.528h30.015c.107-.333.204-.678.301-1.011a34.102 34.102 0 0 1-8.305-.495c2.227-2.732 4.454-5.486 6.68-8.219a1.861 1.861 0 0 0 .14-.161c1.13-1.399 2.27-2.787 3.4-4.185v-.002a49.952 49.952 0 0 0-1.463-12.725Zm-34.37-67.613.015-.022-.016.043Zm-6.65 59.932-.257-.58c.01-.42.01-.84 0-1.27 0-.119-.022-.237-.022-.355.097.742.183 1.484.29 2.227Z"
          />
          <circle cx={95.249} cy={439} r={11} fill="#3f3d56" />
          <circle cx={227.249} cy={559} r={11} fill="#3f3d56" />
          <circle cx={728.249} cy={559} r={11} fill="#3f3d56" />
        </svg>

        <h1 className="text-6xl font-bold mb-4">404</h1>
        <h2 className="text-2xl font-semibold mb-6">Page Not Found</h2>
        <p className="text-muted-foreground max-w-md mb-8">
          The page you're looking for doesn't exist or has been moved.
        </p>

        <div className="flex gap-4 justify-center">
          <Link href="/" className="!no-underline">
            <Button variant="default">
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Button>
          </Link>
          <Link href="/contact" className="!no-underline">
            <Button variant="secondary">
              Contact Support
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
