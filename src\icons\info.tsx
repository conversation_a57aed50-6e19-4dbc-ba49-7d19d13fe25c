import React from "react";

const Info = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.25 8.23502C8.25 8.03611 8.32902 7.84534 8.46967 7.70469C8.61032 7.56404 8.80109 7.48502 9 7.48502C9.19891 7.48502 9.38968 7.56404 9.53033 7.70469C9.67098 7.84534 9.75 8.03611 9.75 8.23502V12.735C9.75 12.9339 9.67098 13.1247 9.53033 13.2653C9.38968 13.406 9.19891 13.485 9 13.485C8.80109 13.485 8.61032 13.406 8.46967 13.2653C8.32902 13.1247 8.25 12.9339 8.25 12.735V8.23502ZM9 4.53827C8.80109 4.53827 8.61032 4.61729 8.46967 4.75794C8.32902 4.89859 8.25 5.08936 8.25 5.28827C8.25 5.48718 8.32902 5.67795 8.46967 5.8186C8.61032 5.95925 8.80109 6.03827 9 6.03827C9.19891 6.03827 9.38968 5.95925 9.53033 5.8186C9.67098 5.67795 9.75 5.48718 9.75 5.28827C9.75 5.08936 9.67098 4.89859 9.53033 4.75794C9.38968 4.61729 9.19891 4.53827 9 4.53827Z"
        fill="currentColor"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M9 1.5C4.85775 1.5 1.5 4.85775 1.5 9C1.5 13.1423 4.85775 16.5 9 16.5C13.1423 16.5 16.5 13.1423 16.5 9C16.5 4.85775 13.1423 1.5 9 1.5ZM3 9C3 10.5913 3.63214 12.1174 4.75736 13.2426C5.88258 14.3679 7.4087 15 9 15C10.5913 15 12.1174 14.3679 13.2426 13.2426C14.3679 12.1174 15 10.5913 15 9C15 7.4087 14.3679 5.88258 13.2426 4.75736C12.1174 3.63214 10.5913 3 9 3C7.4087 3 5.88258 3.63214 4.75736 4.75736C3.63214 5.88258 3 7.4087 3 9Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default Info;
