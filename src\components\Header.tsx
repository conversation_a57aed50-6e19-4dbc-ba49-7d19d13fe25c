'use client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@/components'
import ButtonSignin from '@/components/ButtonSignin'
import NavLinks from '@/components/nav-links'
import {
	Sheet,
	SheetContent,
	Sheet<PERSON>eader,
	SheetTitle,
	SheetTrigger,
} from '@/components/ui/sheet'
import { Blog, Demo, Moon, OpenNav, Pricing, RightArrow, Sun } from '@/icons'
import { ScrollToSection } from '@/utils/scroll-to-section'
import Link from 'next/link'
import { ThemeToggle } from '@/components/theme-toggle'

const nav_links = [
	{
		icon: <Demo />,
		title: 'Demo',
		link: '/',
	},
	{
		icon: <Pricing />,
		title: 'Pricing',
		link: '/',
	},
	{
		icon: <Blog width={18} height={18} />,
		title: 'Blog',
		link: '/blog',
	},
]

const MobileNav = () => (
	<Sheet>
		<SheetTrigger>
			<div className='text-black1 dark:text-white'>
				<OpenNav />
			</div>
		</SheetTrigger>
		<SheetContent className='bg-white dark:bg-black1 px-0 pt-4 border-l-0 min-w-[320px]'>
			<SheetHeader>
				<SheetTitle className='text-black1 dark:text-white text-xl font-bold border-b border-[#b3b3b3] text-left pb-4 pl-4'>
					Menu
				</SheetTitle>
			</SheetHeader>
			<Link href='/' className='flex items-center gap-2 mt-8 mx-auto w-fit'>
				<Logo />
			</Link>
			<div className='my-8 mx-auto w-fit'>
				<NavLinks nav_links={nav_links} />
			</div>
			<div className='mb-8 mx-auto w-fit block'>
				<ButtonSignin />
			</div>
			<div className='mx-auto w-fit block'>
				<ThemeToggle />
			</div>
		</SheetContent>
	</Sheet>
)

const Header = () => (
	<div className='flex justify-center items-center w-full fixed top-0 z-50 bg-white dark:bg-[#010814]'>
		<div className='max-w-[1440px] w-full flex justify-between items-center gap-4 px-4 sm:px-12 py-6'>
			<Link href='/'>
				<Logo />
			</Link>
			{/* Desktop Navigation */}
			<div className='hidden lg:flex items-center gap-8'>
				<NavLinks nav_links={nav_links} />
				<ThemeToggle />
				<ButtonSignin />
			</div>
			{/* Mobile Navigation */}
			<div className='lg:hidden flex items-center gap-2 sm:gap-4'>
				<MobileNav />
			</div>
		</div>
	</div>
)

export default Header
