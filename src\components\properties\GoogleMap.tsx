'use client';

import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

interface Property {
    id: string;
    title: string;
    price: number;
    bedrooms: number;
    bathrooms: number;
    propertyType: string;
    address: string;
    coordinates: {
        lat: number;
        lng: number;
    };
    images?: { url: string; alt?: string }[];
    isMLS?: boolean;
}

interface GoogleMapProps {
    properties: Property[];
    onMapMove: (bounds: mapboxgl.LngLatBounds) => void;
    onPropertyClick?: (property: Property) => void;
    initialCenter?: { lat: number; lng: number };
    initialZoom?: number;
    searchRadius?: number; // in kilometers
    showRadius?: boolean;
}

const GoogleMap: React.FC<GoogleMapProps> = ({
    properties,
    onMapMove,
    onPropertyClick,
    initialCenter = { lat: 43.6532, lng: -79.3832 }, // Default to Toronto
    initialZoom = 11,
    searchRadius = 10,
    showRadius = false,
}) => {
    const mapRef = useRef<HTMLDivElement>(null);
    const map = useRef<mapboxgl.Map | null>(null);
    const markers = useRef<mapboxgl.Marker[]>([]);
    const radiusCircle = useRef<string | null>(null);

    // Clear existing markers
    const clearMarkers = () => {
        markers.current.forEach(marker => marker.remove());
        markers.current = [];
    };

    // Add radius circle
    const addRadiusCircle = (center: { lat: number; lng: number }, radius: number) => {
        if (!map.current) return;

        // Remove existing circle
        if (radiusCircle.current && map.current.getLayer(radiusCircle.current)) {
            map.current.removeLayer(radiusCircle.current);
            map.current.removeSource(radiusCircle.current);
        }

        const circleId = 'search-radius';
        radiusCircle.current = circleId;

        // Create circle coordinates
        const points = 64;
        const coords = [];
        for (let i = 0; i < points; i++) {
            const angle = (i * 360) / points;
            const lat = center.lat + (radius / 111) * Math.cos((angle * Math.PI) / 180);
            const lng = center.lng + (radius / (111 * Math.cos((center.lat * Math.PI) / 180))) * Math.sin((angle * Math.PI) / 180);
            coords.push([lng, lat]);
        }
        coords.push(coords[0]); // Close the circle

        map.current.addSource(circleId, {
            type: 'geojson',
            data: {
                type: 'Feature',
                geometry: {
                    type: 'Polygon',
                    coordinates: [coords]
                },
                properties: {}
            }
        });

        map.current.addLayer({
            id: circleId,
            type: 'fill',
            source: circleId,
            paint: {
                'fill-color': '#3b82f6',
                'fill-opacity': 0.1
            }
        });

        map.current.addLayer({
            id: `${circleId}-border`,
            type: 'line',
            source: circleId,
            paint: {
                'line-color': '#3b82f6',
                'line-width': 2,
                'line-opacity': 0.8
            }
        });
    };

    useEffect(() => {
        if (!mapRef.current) return;

        // Set Mapbox access token
        mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY || 'pk.eyJ1IjoiamF2aWFucGljYXJkbzMzIiwiYSI6ImNtYjY4ZGRyazBiaWYybHEyMWpnNGN4cDQifQ.m63aGFvbfzrQhT3sWlSbDQ';

        // Initialize map
        map.current = new mapboxgl.Map({
            container: mapRef.current,
            style: 'mapbox://styles/mapbox/streets-v12',
            center: [initialCenter.lng, initialCenter.lat],
            zoom: initialZoom,
        });

        // Add navigation controls
        map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');

        // Add fullscreen control
        map.current.addControl(new mapboxgl.FullscreenControl(), 'top-right');

        // Handle map movement
        map.current.on('moveend', () => {
            if (map.current) {
                const bounds = map.current.getBounds();
                onMapMove(bounds);
            }
        });

        // Add radius circle if enabled
        if (showRadius) {
            map.current.on('load', () => {
                addRadiusCircle(initialCenter, searchRadius);
            });
        }

        return () => {
            clearMarkers();
            map.current?.remove();
        };
    }, [initialCenter, initialZoom, onMapMove, searchRadius, showRadius]);

    // Update markers when properties change
    useEffect(() => {
        if (!map.current) return;

        clearMarkers();

        properties.forEach((property) => {
            if (property.coordinates) {
                // Create custom marker element
                const markerElement = document.createElement('div');
                markerElement.className = 'custom-marker';
                markerElement.style.cssText = `
                    width: 40px;
                    height: 40px;
                    background: ${property.isMLS ? '#3b82f6' : '#10b981'};
                    border: 3px solid white;
                    border-radius: 50%;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                    color: white;
                    font-size: 12px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                    transition: transform 0.2s;
                `;
                markerElement.textContent = property.isMLS ? 'MLS' : '$';

                // Add hover effect
                markerElement.addEventListener('mouseenter', () => {
                    markerElement.style.transform = 'scale(1.1)';
                });
                markerElement.addEventListener('mouseleave', () => {
                    markerElement.style.transform = 'scale(1)';
                });

                // Create popup content
                const popupContent = `
                    <div style="min-width: 250px;">
                        ${property.images && property.images.length > 0 ?
                        `<img src="${property.images[0].url}" alt="${property.images[0].alt || property.title}" style="width: 100%; height: 150px; object-fit: cover; border-radius: 8px; margin-bottom: 12px;" />`
                        : ''
                    }
                        <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: bold;">$${property.price.toLocaleString()}</h3>
                        <div style="display: flex; gap: 8px; margin-bottom: 8px; font-size: 14px; color: #666;">
                            <span>${property.bedrooms}BD</span>
                            <span>|</span>
                            <span>${property.bathrooms}BA</span>
                            <span>|</span>
                            <span>${property.propertyType}</span>
                        </div>
                        <p style="margin: 0 0 8px 0; font-size: 14px; color: #666;">${property.address}</p>
                        ${property.isMLS ? '<span style="background: #3b82f6; color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px;">MLS®</span>' : '<span style="background: #10b981; color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px;">Private</span>'}
                    </div>
                `;

                const marker = new mapboxgl.Marker(markerElement)
                    .setLngLat([property.coordinates.lng, property.coordinates.lat])
                    .setPopup(
                        new mapboxgl.Popup({
                            offset: 25,
                            closeButton: true,
                            closeOnClick: false
                        }).setHTML(popupContent)
                    )
                    .addTo(map.current);

                // Handle marker click
                markerElement.addEventListener('click', () => {
                    if (onPropertyClick) {
                        onPropertyClick(property);
                    }
                });

                markers.current.push(marker);
            }
        });
    }, [properties, onPropertyClick]);

    return (
        <div
            ref={mapRef}
            style={{ height: '500px', width: '100%' }}
            className="rounded-lg overflow-hidden shadow-lg"
        />
    );
};

export default GoogleMap;
