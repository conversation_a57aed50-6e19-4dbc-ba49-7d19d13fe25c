import { useState } from 'react';
import { Button } from '@/components/ui/button';

// Placeholder data and actions
const mockUsers = [
  { id: '2', name: '<PERSON>', email: '<EMAIL>', role: 'product' },
  { id: '4', name: '<PERSON>', email: '<EMAIL>', role: 'product' },
];

export default function ProductManagement() {
  const [users, setUsers] = useState(mockUsers);
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAdd = () => {
    setLoading(true);
    // TODO: Call backend to add user with product role
    setTimeout(() => {
      setUsers([...users, { id: Date.now().toString(), name: email.split('@')[0], email, role: 'product' }]);
      setEmail('');
      setLoading(false);
    }, 800);
  };

  return (
    <div className="max-w-2xl mx-auto py-10">
      <h2 className="text-2xl font-bold mb-6">Product Team: Manage Members</h2>
      <div className="mb-8 flex gap-2 items-end">
        <input
          type="email"
          placeholder="<EMAIL>"
          value={email}
          onChange={e => setEmail(e.target.value)}
          className="border rounded px-3 py-2 w-64"
        />
        <Button onClick={handleAdd} disabled={loading || !email} className="ml-2">
          {loading ? 'Adding...' : 'Add Product Member'}
        </Button>
      </div>
      <table className="w-full border rounded shadow">
        <thead>
          <tr className="bg-gray-2">
            <th className="p-2 text-left">Name</th>
            <th className="p-2 text-left">Email</th>
          </tr>
        </thead>
        <tbody>
          {users.map(u => (
            <tr key={u.id} className="border-t">
              <td className="p-2">{u.name}</td>
              <td className="p-2">{u.email}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
} 