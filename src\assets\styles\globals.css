@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --radius: 0.5rem;
  --background: oklch(1 0 0); /* White for light mode */
  --foreground: oklch(0.141 0.005 285.823); /* Near black for light mode */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: var(--orange-600); /* Use orange for primary */
  --primary-foreground: oklch(0.982 0.018 155.826); /* White-ish for primary foreground */
  --secondary: var(--green-600); /* Use green for secondary */
  --secondary-foreground: oklch(0.982 0.018 155.826); /* White-ish for secondary foreground */
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: 228 228 231; /* zinc-200 for borders in light mode */
  --input: 228 228 231; /* zinc-200 for input borders in light mode */
  --ring: var(--orange-600); /* Use orange for ring */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.723 0.219 149.579);
  --sidebar-primary-foreground: oklch(0.982 0.018 155.826);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.723 0.219 149.579);

  /* Custom Orange Scale */
  --orange-50: oklch(0.99 0.01 60);
  --orange-100: oklch(0.97 0.03 60);
  --orange-200: oklch(0.94 0.05 60);
  --orange-300: oklch(0.90 0.08 60);
  --orange-400: oklch(0.85 0.12 60);
  --orange-500: oklch(0.80 0.15 60);
  --orange-600: oklch(0.67 0.18 60);
  --orange-700: oklch(0.58 0.20 60);
  --orange-800: oklch(0.48 0.18 60);
  --orange-900: oklch(0.38 0.15 60);
  --orange-950: oklch(0.25 0.10 60);

  /* Custom Green Scale */
  --green-50: oklch(0.99 0.01 142);
  --green-100: oklch(0.97 0.03 142);
  --green-200: oklch(0.94 0.05 142);
  --green-300: oklch(0.88 0.08 142);
  --green-400: oklch(0.82 0.12 142);
  --green-500: oklch(0.75 0.15 142);
  --green-600: oklch(0.67 0.18 142);
  --green-700: oklch(0.58 0.20 142);
  --green-800: oklch(0.48 0.18 142);
  --green-900: oklch(0.38 0.15 142);
  --green-950: oklch(0.25 0.10 142);

  /* Custom Gray Scale */
  --gray-50: oklch(0.99 0 0);
  --gray-100: oklch(0.97 0 0);
  --gray-200: oklch(0.94 0 0);
  --gray-300: oklch(0.91 0 0);
  --gray-400: oklch(0.88 0 0);
  --gray-500: oklch(0.85 0 0);
  --gray-600: oklch(0.55 0 0);
  --gray-700: oklch(0.45 0 0);
  --gray-800: oklch(0.35 0 0);
  --gray-900: oklch(0.25 0 0);
  --gray-950: oklch(0.12 0 0);
}

.dark {
  --background: var(--gray-950); /* Classic dark grey for dark mode background */
  --foreground: oklch(0.985 0 0); /* White-ish for dark mode foreground */
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: var(--orange-600); /* Use orange for primary */
  --primary-foreground: oklch(0.982 0.018 155.826); /* White-ish for primary foreground */
  --secondary: var(--green-600); /* Use green for secondary */
  --secondary-foreground: oklch(0.982 0.018 155.826); /* White-ish for secondary foreground */
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: 113 113 122; /* zinc-500 for borders in dark mode */
  --input: 113 113 122; /* zinc-500 for input borders in dark mode */
  --ring: var(--orange-600); /* Use orange for ring */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.696 0.17 162.48);
  --sidebar-primary-foreground: oklch(0.393 0.095 152.535);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.527 0.154 150.069);

  /* Custom Orange Scale Dark */
  --orange-50: oklch(0.25 0.10 60);
  --orange-100: oklch(0.30 0.12 60);
  --orange-200: oklch(0.35 0.14 60);
  --orange-300: oklch(0.42 0.16 60);
  --orange-400: oklch(0.50 0.18 60);
  --orange-500: oklch(0.60 0.20 60);
  --orange-600: oklch(0.70 0.18 60);
  --orange-700: oklch(0.78 0.15 60);
  --orange-800: oklch(0.85 0.12 60);
  --orange-900: oklch(0.90 0.08 60);
  --orange-950: oklch(0.95 0.05 60);

  /* Custom Green Scale Dark */
  --green-50: oklch(0.25 0.10 142);
  --green-100: oklch(0.30 0.12 142);
  --green-200: oklch(0.35 0.14 142);
  --green-300: oklch(0.42 0.16 142);
  --green-400: oklch(0.50 0.18 142);
  --green-500: oklch(0.60 0.20 142);
  --green-600: oklch(0.70 0.18 142);
  --green-700: oklch(0.78 0.15 142);
  --green-800: oklch(0.85 0.12 142);
  --green-900: oklch(0.90 0.08 142);
  --green-950: oklch(0.95 0.05 142);

  /* Custom Gray Scale Dark */
  --gray-50: oklch(0.08 0 0);
  --gray-100: oklch(0.11 0 0);
  --gray-200: oklch(0.15 0 0);
  --gray-300: oklch(0.19 0 0);
  --gray-400: oklch(0.23 0 0);
  --gray-500: oklch(0.28 0 0);
  --gray-600: oklch(0.65 0 0);
  --gray-700: oklch(0.75 0 0);
  --gray-800: oklch(0.85 0 0);
  --gray-900: oklch(0.90 0 0);
  --gray-950: oklch(0.94 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring;
  }
  body {
    @apply bg-background min-h-screen;
    color: #4b5563 !important; /* text-gray-600 */
    font-weight: 500 !important; /* font-medium */
    transition: background-color 0.2s ease, color 0.2s ease;
  }

  /* Layout improvements */
  html {
    @apply bg-background;
  }

  main {
    @apply bg-background;
    color: #4b5563 !important; /* text-gray-600 */
    font-weight: 500 !important; /* font-medium */
  }

  footer {
    @apply bg-background border-t border-border;
    color: #4b5563 !important; /* text-gray-600 */
    font-weight: 500 !important; /* font-medium */
  }

  /* Header specific styling - NO TRANSPARENCY OR BLUR */
  header {
    background: rgb(255, 255, 255) !important; /* Solid white background */
    border-bottom: 1px solid rgb(var(--border)) !important;
    min-height: 2.5rem;
    height: auto;
    display: flex;
    align-items: center;
    backdrop-filter: none !important;
  }

  /* Typography improvements */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.5rem;
    color: #4b5563 !important; /* text-gray-600 */
  }

  h1 { font-size: 2.25rem; }
  h2 { font-size: 1.875rem; }
  h3 { font-size: 1.5rem; }
  h4 { font-size: 1.25rem; }
  h5 { font-size: 1.125rem; }
  h6 { font-size: 1rem; }

  p {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: #4b5563 !important; /* text-gray-600 */
    font-weight: 500 !important; /* font-medium */
  }

  /* Ultra-slim Form elements - Consistent h-8 px-1 styling */
  button, .btn, input, select, textarea {
    min-height: 2rem;
    height: 2rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 0.25rem;
  }

  select, input[type="text"], input[type="search"], input[type="email"], input[type="password"], input[type="number"] {
    height: 2rem;
    padding: 0 0.25rem;
    border-radius: 0.375rem;
    border: 1px solid rgb(var(--border));
    background-color: var(--background);
    color: #4b5563 !important; /* text-gray-600 */
    font-weight: 500 !important; /* font-medium */
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  /* Shadcn UI component consistency - Ultra-slim */
  [data-radix-select-trigger],
  [data-radix-dropdown-menu-trigger],
  [role="button"],
  .button,
  span[role="button"] {
    height: 2rem !important;
    min-height: 2rem !important;
    padding: 0 0.25rem !important;
  }

  /* Labels and text elements */
  label, .label {
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.25rem;
    margin-bottom: 0.25rem;
    color: #4b5563 !important; /* text-gray-600 */
  }

  select:focus, input:focus {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
    border-color: var(--ring);
  }

  /* Navigation improvements */
  nav a, .nav-item {
    padding: 0.5rem 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #4b5563 !important; /* text-gray-600 */
    font-weight: 500 !important; /* font-medium */
  }

  /* Clean dropdown styling - NO SPECIAL BACKGROUNDS */
  [data-radix-dropdown-menu-content] {
    background: var(--background) !important;
    backdrop-filter: none !important;
    opacity: 1 !important;
    z-index: 9999 !important;
    border: 1px solid rgb(var(--border)) !important;
  }

  [data-radix-dropdown-menu-label] {
    color: #4b5563 !important; /* text-gray-600 */
    font-weight: 500 !important; /* font-medium */
  }

  [data-radix-dropdown-menu-separator] {
    background: rgb(var(--border)) !important;
    opacity: 1 !important;
  }

  /* Force solid backgrounds on all dropdown elements */
  .dropdown-menu-content,
  [role="menu"],
  [data-state="open"][data-side] {
    background: var(--background) !important;
    backdrop-filter: none !important;
  }
  :root {
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer components {
  /* Container improvements */
  .container {
    @apply px-4 mx-auto max-w-7xl;
  }

  @media (min-width: 640px) {
    .container {
      @apply px-8;
    }
  }

  @media (min-width: 1024px) {
    .container {
      @apply px-16;
    }
  }

  @media (min-width: 1280px) {
    .container {
      @apply px-20;
    }
  }

  /* Property search and listing improvements */
  .property-search-section {
    @apply py-8 bg-background;
  }

  .property-search-form {
    @apply flex flex-wrap gap-4 items-end p-6 bg-card border border-border rounded-xl shadow-sm;
  }

  .property-search-form > * {
    @apply min-w-48 flex-1;
  }

  .property-search-form button {
    @apply bg-primary text-primary-foreground border-0 px-6 py-3 rounded-md font-medium cursor-pointer transition-colors hover:bg-primary/90;
  }

  /* Property cards */
  .property-card {
    @apply bg-card border border-border rounded-xl overflow-hidden shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-1;
  }

  /* Navigation menu improvements */
  .nav-menu {
    @apply flex items-center gap-4 px-4 py-2;
  }

  .nav-menu a {
    @apply px-4 py-2 rounded-md no-underline font-medium transition-colors hover:bg-accent hover:text-accent-foreground;
    color: #4b5563 !important; /* text-gray-600 */
  }

  /* Ultra-slim Button improvements - Modern gradient styling with primary colors */
  .btn {
    @apply inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-8 rounded-xl border shadow px-1;
  }

  .btn-primary {
    background: linear-gradient(135deg, var(--primary), var(--primary) / 80%) !important;
    color: var(--primary-foreground) !important;
    border-color: var(--primary) / 20% !important;
    @apply h-8 px-1 rounded-xl shadow-md hover:shadow-lg transition-all duration-200;
  }

  .btn-secondary {
    background: linear-gradient(135deg, var(--primary) / 10%, var(--accent) / 5%) !important;
    color: var(--primary) !important;
    border: 1px solid rgb(var(--border)) !important;
    @apply h-8 px-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200;
  }

  .btn-outline {
    background: transparent !important;
    color: var(--primary) !important;
    border: 1px solid rgb(var(--border)) !important;
    @apply h-8 px-1 rounded-xl shadow-sm hover:bg-primary/10 transition-all duration-200;
  }

  /* Consistent card-like styling for all components */
  .card-style {
    @apply rounded-xl border bg-card text-card-foreground shadow;
  }

  /* Apply card styling to form elements */
  input, select, textarea {
    @apply rounded-xl border shadow;
  }
}

@theme inline {
  @keyframes accordion-down {
  from {
    height: 0;
    }
  to {
    height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
    }
  to {
    height: 0;
    }
  }
  @keyframes rainbow {
  0% {
    background-position: 0%;
    }
  100% {
    background-position: 200%;
    }
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
