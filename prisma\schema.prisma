generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["snb"]
}

enum UserType {
  buyer
  seller
  service_provider
  admin
  product

  @@schema("snb")
}

enum PropertyStatus {
  pending
  active
  sold
  expired

  @@schema("snb")
}

enum ServiceType {
  lawyer
  photographer
  inspector

  @@schema("snb")
}

enum BookingStatus {
  pending
  confirmed
  completed
  cancelled

  @@schema("snb")
}

enum OfferStatus {
  pending
  reviewed
  accepted
  rejected

  @@schema("snb")
}

enum AccessType {
  qr_scan
  online_access

  @@schema("snb")
}

model User {
  id            String    @id @default(uuid())
  email         String    @unique
  fullName      String
  phone         String?
  address       Json?
  userType      UserType
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastLoginAt   DateTime?
  loggedIn      Boolean   @default(false)
  properties    Property[]        @relation("SellerProperties")
  subscriptions Subscription[]
  serviceProvider ServiceProvider?
  bookings      ServiceBooking[]
  messages      Message[]         @relation("UserMessages")
  viewings      PropertyViewing[] @relation("BuyerViewings")
  buyerProfile  BuyerProfile?  @relation("UserBuyerProfile")
  sellerProfile SellerProfile? @relation("UserSellerProfile")
  buyerListings BuyerListings[] @relation(name: "BuyerListings")
  searchFilters SearchFilter[] @relation(name: "SearchFilters")

  @@schema("snb")
}

model Property {
  id            String    @id @default(uuid())
  seller        User      @relation(fields: [sellerId], references: [id], name: "SellerProperties")
  sellerId      String
  title         String
  description   String?
  price         Decimal
  bedrooms      Int?
  bathrooms     Int?
  sqft          Int?
  propertyType  String
  address       Json
  coordinates   Json?
  features      Json?
  status        PropertyStatus @default(pending)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  expiresAt     DateTime?
  images        PropertyImage[]
  bookings      ServiceBooking[]
  viewings      PropertyViewing[]
  conversations Conversation[]
  listedByBuyer Boolean @default(false)
  buyerId String?
  amenities Json?
  yearBuilt Int?
  lotSize Int?
  buyerListings BuyerListings[] @relation(name: "PropertyListings")

  // Enhanced property fields for comprehensive listings
  adType        String? // For Sale, Sold, Delisted
  storeys       Int?
  parkingTypes  Json? // Array of parking types
  extraFeatures Json? // Array of extra features like wheelchair accessible, hot tub, etc.
  mlsNumber     String? // MLS listing number
  listingId     String? // Custom listing ID
  province      String? // Province/State
  postalCode    String? // Postal/ZIP code
  city          String? // City name
  neighborhood  String? // Neighborhood/area
  lotSizeUnit   String? // sqft, acres, etc.
  priceHistory  Json? // Price change history
  virtualTour   String? // Virtual tour URL
  openHouse     Json? // Open house schedule
  isLiked       Boolean @default(false) // For user likes
  likedBy       Json? // Array of user IDs who liked this property
  distanceFilters Json? // Distance-based search filters

  // AI-powered features
  aiGeneratedDescription String? // AI-enhanced property description
  aiReports             Json? // AI-generated neighborhood reports
  openHouseQrData       Json? // QR code data for open house access
  walkabilityScore      Int? // Walkability score from AI analysis
  neighborhoodScore     Int? // Overall neighborhood score
  aiAnalysisGenerated   Boolean @default(false) // Whether AI analysis has been generated
  aiGeneratedAt         DateTime? // When AI analysis was last generated

  // Relations for new AI features
  openHouseAccesses     OpenHouseAccess[]
  buyerOffers          BuyerOffer[]
  aiReportsRelation    AIReport[]

  @@schema("snb")
}

model BuyerListings {
  id String @id @default(uuid())
  buyer User @relation(fields: [buyerId], references: [id], name: "BuyerListings")
  buyerId String
  property Property @relation(fields: [propertyId], references: [id], name: "PropertyListings")
  propertyId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@schema("snb")
}

model SearchFilter {
  id String @id @default(uuid())
  userId String
  user User @relation(fields: [userId], references: [id], name: "SearchFilters")
  filterName String
  filterData Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@schema("snb")
}

model PropertyImage {
  id           String   @id @default(uuid())
  property     Property @relation(fields: [propertyId], references: [id])
  propertyId   String
  url          String
  storagePath  String
  isPrimary    Boolean  @default(false)
  createdAt    DateTime @default(now())

  @@schema("snb")
}

model Subscription {
  id                 String    @id @default(uuid())
  user               User      @relation(fields: [userId], references: [id])
  userId             String
  stripeSubscriptionId String
  status             String
  planType           String
  startsAt           DateTime
  endsAt             DateTime
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@schema("snb")
}

model ServiceProvider {
  id           String      @id @default(uuid())
  user         User        @relation(fields: [userId], references: [id])
  userId       String      @unique
  serviceType  ServiceType
  businessName String
  description  String?
  licenseNumber String?
  regions      Json?
  rates        Json?
  rating       Decimal?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  bookings     ServiceBooking[]

  @@schema("snb")
}

model ServiceBooking {
  id                String         @id @default(uuid())
  property          Property       @relation(fields: [propertyId], references: [id])
  propertyId        String
  serviceProvider   ServiceProvider @relation(fields: [serviceProviderId], references: [id])
  serviceProviderId String
  user              User           @relation(fields: [userId], references: [id])
  userId            String
  serviceType       ServiceType
  status            BookingStatus  @default(pending)
  scheduledAt       DateTime?
  completedAt       DateTime?
  notes             String?
  paymentId         String?
  createdAt         DateTime       @default(now())
  updatedAt         DateTime @updatedAt

  @@schema("snb")
}

model PropertyViewing {
  id           String   @id @default(uuid())
  property     Property @relation(fields: [propertyId], references: [id])
  propertyId   String
  buyer        User     @relation(fields: [buyerId], references: [id], name: "BuyerViewings")
  buyerId      String
  status       String   @default("requested")
  proposedTimes Json?
  confirmedTime DateTime?
  notes        String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@schema("snb")
}

model Conversation {
  id           String   @id @default(uuid())
  property     Property? @relation(fields: [propertyId], references: [id])
  propertyId   String?
  participants Json
  lastMessageAt DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  messages     Message[]

  @@schema("snb")
}

model Message {
  id             String        @id @default(uuid())
  conversation   Conversation  @relation(fields: [conversationId], references: [id])
  conversationId String
  sender         User          @relation(fields: [senderId], references: [id], name: "UserMessages")
  senderId       String
  message        String
  read           Boolean       @default(false)
  createdAt      DateTime      @default(now())

  @@schema("snb")
}

model BuyerProfile {
  id        String  @id @default(uuid())
  user      User    @relation("UserBuyerProfile", fields: [userId], references: [id])
  userId    String  @unique
  // Add buyer-specific fields here
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@schema("snb")
}

model Project {
  id            String   @id @default(uuid())
  connection_id String
  webhook_id    String
  scenario_id   String
  user_clerk_id String
  webhookLink   String
  assistant_id  String?
  type          String
  status        String   @default("default")
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@schema("snb")
}

model SellerProfile {
  id        String  @id @default(uuid())
  user      User    @relation("UserSellerProfile", fields: [userId], references: [id])
  userId    String  @unique
  // Add seller-specific fields here
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@schema("snb")
}

// AI-related models
model OpenHouseAccess {
  id         String    @id @default(uuid())
  property   Property  @relation(fields: [propertyId], references: [id])
  propertyId String
  buyerId    String?
  buyerInfo  Json?     // Buyer contact information
  accessType AccessType
  accessedAt DateTime  @default(now())
  ipAddress  String?
  userAgent  String?

  @@schema("snb")
}

model BuyerOffer {
  id         String      @id @default(uuid())
  property   Property    @relation(fields: [propertyId], references: [id])
  propertyId String
  buyerId    String
  offerAmount Decimal
  conditions Json?       // Array of conditions like "Home Inspection", "Financing"
  message    String?
  status     OfferStatus @default(pending)
  submittedAt DateTime   @default(now())
  expiresAt  DateTime?
  reviewedAt DateTime?
  respondedAt DateTime?
  sellerResponse String?

  @@schema("snb")
}

model AIReport {
  id          String   @id @default(uuid())
  property    Property @relation(fields: [propertyId], references: [id])
  propertyId  String
  reportType  String   // schools, shopping, dining, etc.
  title       String
  description String
  data        Json     // Report-specific data
  score       Int?     // Optional score (1-100)
  generatedAt DateTime @default(now())
  lastUpdated DateTime @updatedAt

  @@schema("snb")
}
