import { redirect } from 'next/navigation'
import { Metadata } from 'next'

const SUPPORTED_COUNTRIES = ['us', 'ca']

interface CountryPageProps {
  params: Promise<{
    country: string
  }>
}

export async function generateMetadata({ params }: CountryPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const country = resolvedParams.country.toLowerCase()
  const countryName = country === 'ca' ? 'Canada' : country === 'us' ? 'United States' : 'Unknown'

  return {
    title: `SoNo Brokers - ${countryName}`,
    description: `Find your perfect home in ${countryName} with SoNo Brokers`,
  }
}

export default async function CountryPage({ params }: CountryPageProps) {
  const resolvedParams = await params
  const country = resolvedParams.country.toLowerCase()

  // Validate country
  if (!SUPPORTED_COUNTRIES.includes(country)) {
    redirect(`/unsupported-region?country=${country.toUpperCase()}`)
  }

  // Redirect to home page for now - this is where country-specific content would go
  redirect('/')
}
