'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import GoogleMap from './GoogleMap';
import { Button } from '@/components/ui/button';
import { ShowcaseCard } from '@/components/ui/showcase-card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Sheet, SheetContent, SheetTrigger, SheetClose, SheetHeader, SheetTitle, SheetDescription, SheetFooter } from '@/components/ui/sheet';
import { useIsMobile } from '@/hooks/use-mobile';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Property } from '@/types/property';
import { Slider } from "@/components/ui/slider";
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
    Pagin<PERSON>,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from '@/components/ui/pagination';
import {
    Search,
    MapPin,
    Home,
    SlidersHorizontal,
    Heart,
    Grid3X3,
    List,
    Map,
    Save,
    Trash2
} from 'lucide-react';

import { cn } from '@/lib/utils';

interface ClientSavedSearch {
    id: string;
    name: string;
    filters: {
        location: string;
        mlsNumber: string;
        propertyType: string;
        adType: string;
        priceRange: number[];
        yearBuiltRange: number[];
        bedrooms: string;
        bathrooms: string;
        storeys: string;
        parkingTypes: string[];
        extraFeatures: string[];
    };
    createdAt?: string;
}

interface DbSearchFilter {
    id: string;
    userId: string;
    filterName: string;
    filterData: ClientSavedSearch['filters'];
    createdAt?: Date;
    updatedAt?: Date;
}

interface PropertySearchClientProps {
    initialProperties: Property[];
}

async function executeSupabaseSqlMcp(query: string, callingFunctionInfo: string): Promise<any[]> {
    console.info(
        `[${callingFunctionInfo}] INTENDS to execute SQL via MCP tool:\n` +
        `Query: ${query}\n` +
        `The AI (Cline) should now provide a <use_mcp_tool> instruction with:\n` +
        `Server: github.com/supabase-community/supabase-mcp\n` +
        `Tool: execute_sql\n` +
        `Arguments: { "project_id": "yfznlsisxsnymkvydzha", "query": (the SQL query above) }`
    );
    console.warn("executeSupabaseSqlMcp: If no MCP call is triggered by the AI, this will use localStorage fallback.");
    if (typeof window !== 'undefined' && window.localStorage) {
        const userIdMatch = query.match(/'(user_[^']+)'/);
        const userId = userIdMatch ? userIdMatch[1] : null;
        const storageKey = userId ? `savedSearches_${userId}_supabase_placeholder` : `savedSearches_unknownUser_supabase_placeholder`;

        if (query.startsWith('SELECT')) {
            const stored = localStorage.getItem(storageKey);
            console.log(`[LocalStorage Fallback for SELECT from ${storageKey}]:`, stored ? JSON.parse(stored) : []);
            return stored ? JSON.parse(stored) : [];
        } else if (query.startsWith('INSERT')) {
            const valuesMatch = query.match(/VALUES \('([^']*)', '([^']*)', '([^']*)', '([^']*)'::jsonb, NOW\(\), NOW\(\)\)/);
            if (valuesMatch) {
                const newRecord: DbSearchFilter = {
                    id: valuesMatch[1], userId: valuesMatch[2], filterName: valuesMatch[3].replace(/''/g, "'"),
                    filterData: JSON.parse(valuesMatch[4]), createdAt: new Date(), updatedAt: new Date()
                };
                const currentSaved = JSON.parse(localStorage.getItem(storageKey) || '[]') as DbSearchFilter[];
                currentSaved.push(newRecord);
                localStorage.setItem(storageKey, JSON.stringify(currentSaved));
                console.log(`[LocalStorage Fallback for INSERT to ${storageKey}]:`, newRecord);
                return [{ id: newRecord.id, filterName: newRecord.filterName, filterData: newRecord.filterData, createdAt: newRecord.createdAt.toISOString() }];
            }
        } else if (query.startsWith('DELETE')) {
            const idMatch = query.match(/id = '([^']+)'/);
            const userIdForDeleteMatch = query.match(/"userId" = '([^']+)'/);
            if (idMatch && idMatch[1] && userIdForDeleteMatch && userIdForDeleteMatch[1]) {
                const idToDelete = idMatch[1];
                let currentSaved = JSON.parse(localStorage.getItem(storageKey) || '[]') as DbSearchFilter[];
                currentSaved = currentSaved.filter(s => s.id !== idToDelete);
                localStorage.setItem(storageKey, JSON.stringify(currentSaved));
                console.log(`[LocalStorage Fallback for DELETE from ${storageKey}]: id ${idToDelete}`);
                return [];
            }
        }
    }
    console.error("executeSupabaseSqlMcp: LocalStorage fallback failed or query type not supported.");
    return Promise.resolve([]);
}


const PropertySearchClient: React.FC<PropertySearchClientProps> = ({ initialProperties }) => {
    const router = useRouter();
    const { user, isSignedIn } = useUser();
    const searchParams = useSearchParams();
    const isMobile = useIsMobile();
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [properties, setProperties] = useState<Property[]>(initialProperties);
    const [loading, setLoading] = useState(false);

    const [location, setLocation] = useState(searchParams.get('location') || '');
    const [mlsNumber, setMlsNumber] = useState(searchParams.get('mlsNumber') || '');
    const [propertyType, setPropertyType] = useState(searchParams.get('propertyType') || 'all');
    const [adType, setAdType] = useState(searchParams.get('adType') || 'all');
    const [priceRange, setPriceRange] = useState([
        parseInt(searchParams.get('minPrice') || '0'),
        parseInt(searchParams.get('maxPrice') || '2000000')
    ]);
    const [yearBuiltRange, setYearBuiltRange] = useState([
        parseInt(searchParams.get('minYear') || '1900'),
        parseInt(searchParams.get('maxYear') || '2024')
    ]);
    const [bedrooms, setBedrooms] = useState(searchParams.get('bedrooms') || 'any');
    const [bathrooms, setBathrooms] = useState(searchParams.get('bathrooms') || 'any');
    const [storeys, setStoreys] = useState(searchParams.get('storeys') || '');
    const [parkingTypes, setParkingTypes] = useState<string[]>(
        searchParams.get('parkingTypes')?.split(',').filter(Boolean) || []
    );
    const [extraFeatures, setExtraFeatures] = useState<string[]>(
        searchParams.get('extraFeatures')?.split(',').filter(Boolean) || []
    );
    const [sort, setSort] = useState(searchParams.get('sort') || 'created_at.desc');
    const [mapBounds, setMapBounds] = useState<any>(null);
    const [activeViews, setActiveViews] = useState<Set<'grid' | 'list' | 'map'>>(new Set(['grid']));
    const [likedProperties, setLikedProperties] = useState<Set<string>>(new Set());
    const [showAdvanced, setShowAdvanced] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const propertiesPerPage = 10;
    const [searchRadius, setSearchRadius] = useState(10);
    const [showRadius, setShowRadius] = useState(false);
    const [mapCenter, setMapCenter] = useState({ lat: 43.6532, lng: -79.3832 });

    const [savedSearches, setSavedSearches] = useState<ClientSavedSearch[]>([]);
    const [isFetchingSavedSearches, setIsFetchingSavedSearches] = useState(false);

    const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

    const handleViewToggle = (view: 'grid' | 'list' | 'map') => {
        const newActiveViews = new Set(activeViews);

        if (newActiveViews.has(view)) {
            // Only allow deselection if there's more than one view active
            if (newActiveViews.size > 1) {
                newActiveViews.delete(view);
            }
        } else {
            newActiveViews.add(view);
        }

        setActiveViews(newActiveViews);
    };

    const handleCardClick = (propertyId: string) => {
        router.push(`/properties/${propertyId}`);
    };

    const handleCardDetailsLinkClick = (propertyId: string) => {
        window.open(`/properties/${propertyId}`, '_blank');
    };

    const fetchSavedSearchesFromSupabase = useCallback(async () => {
        console.log("[PropertySearchClient] Attempting to call fetchSavedSearchesFromSupabase. User signed in:", isSignedIn, "User object:", user);
        if (!isSignedIn || !user) {
            setSavedSearches([]);
            setIsFetchingSavedSearches(false); // Ensure loading state is reset
            return;
        }
        setIsFetchingSavedSearches(true);
        try {
            const query = `SELECT id, "filterName", "filterData", "createdAt" FROM snb."SearchFilter" WHERE "userId" = '${user.id}' ORDER BY "createdAt" DESC;`;
            const dbResults = await executeSupabaseSqlMcp(query, "fetchSavedSearchesFromSupabase") as DbSearchFilter[];

            setSavedSearches(dbResults.map((item: DbSearchFilter) => ({
                id: item.id,
                name: item.filterName,
                filters: item.filterData,
                createdAt: item.createdAt ? new Date(item.createdAt).toISOString() : undefined,
            })).sort((a, b) => {
                const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
                const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
                return dateB - dateA;
            }));

        } catch (e) {
            console.error("Failed to fetch saved searches from Supabase:", e);
        } finally {
            setIsFetchingSavedSearches(false);
        }
    }, [isSignedIn, user]);

    useEffect(() => {
        fetchSavedSearchesFromSupabase();
    }, [fetchSavedSearchesFromSupabase]);

    const parkingOptions = [
        'Assigned Parking', 'Attached Garage', 'Carport', 'Covered Parking',
        'Detached Garage', 'Driveway', 'Indoor Parking', 'Other Parking',
        'Parking Lot', 'Parking Pad', 'Parking Stall', 'Street Parking',
        'Underground', 'Visitor Parking'
    ];

    const extraFeaturesOptions = [
        'Wheelchair Accessible', 'Hot Tub', 'Guest Suite', 'MLS® System',
        'Fireplace', 'Pool', 'Legal Suite', 'Wood Stove', 'Games Room',
        'Private Entrance', 'A/C', 'Home Theatre', 'Open House',
        'Jacuzzi', 'Walkout', 'Virtual Tour/Video'
    ];

    const fetchProperties = useCallback(async () => {
        setLoading(true);
        try {
            const params = new URLSearchParams();
            if (location && location.trim()) params.set('location', location.trim());
            if (mlsNumber && mlsNumber.trim()) params.set('mlsNumber', mlsNumber.trim());
            if (propertyType && propertyType !== 'all') params.set('propertyType', propertyType);
            if (adType && adType !== 'all') params.set('adType', adType);
            if (priceRange[0] > 0) params.set('minPrice', priceRange[0].toString());
            if (priceRange[1] < 2000000) params.set('maxPrice', priceRange[1].toString());
            if (yearBuiltRange[0] > 1900) params.set('minYear', yearBuiltRange[0].toString());
            if (yearBuiltRange[1] < 2024) params.set('maxYear', yearBuiltRange[1].toString());
            if (bedrooms && bedrooms !== 'any') params.set('bedrooms', bedrooms);
            if (bathrooms && bathrooms !== 'any') params.set('bathrooms', bathrooms);
            if (storeys) params.set('storeys', storeys);
            if (parkingTypes.length > 0) params.set('parkingTypes', parkingTypes.join(','));
            if (extraFeatures.length > 0) params.set('extraFeatures', extraFeatures.join(','));
            if (sort) params.set('sort', sort);
            if (mapBounds) {
                params.set('northEastLat', mapBounds.getNorthEast().lat.toString());
                params.set('northEastLng', mapBounds.getNorthEast().lng.toString());
                params.set('southWestLat', mapBounds.getSouthWest().lat.toString());
                params.set('southWestLng', mapBounds.getSouthWest().lng.toString());
            }
            const queryString = params.toString();
            const apiUrl = queryString ? `/api/properties/search?${queryString}` : '/api/properties/search';
            const response = await fetch(apiUrl);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            setProperties(data);
            const newUrl = queryString ? `/properties?${queryString}` : '/properties';
            router.push(newUrl, { scroll: false });
        } catch (error) {
            console.error('Error fetching properties:', error);
            setProperties([]);
        } finally {
            setLoading(false);
        }
    }, [location, mlsNumber, propertyType, adType, priceRange, yearBuiltRange, bedrooms, bathrooms, storeys, parkingTypes, extraFeatures, sort, mapBounds, router]);

    useEffect(() => {
        if (initialProperties.length === 0) {
            fetchProperties();
        }
    }, []);

    const handleSearch = () => {
        fetchProperties();
    };

    const handleSaveSearch = async () => {
        if (!isSignedIn || !user) {
            router.push('/sign-in?redirect=/properties');
            return;
        }
        const searchName = prompt('Enter a name for this search:');
        if (!searchName || searchName.trim() === '') {
            alert('Search name cannot be empty.');
            return;
        }
        const currentFiltersForDB: ClientSavedSearch['filters'] = {
            location, mlsNumber, propertyType, adType, priceRange,
            yearBuiltRange, bedrooms, bathrooms, storeys, parkingTypes, extraFeatures,
        };
        const newSearchId = crypto.randomUUID();
        const newDbFilterData: Omit<DbSearchFilter, 'createdAt' | 'updatedAt'> = {
            id: newSearchId, userId: user.id, filterName: searchName.trim(), filterData: currentFiltersForDB,
        };
        try {
            const safeSearchName = newDbFilterData.filterName.replace(/'/g, "''");
            const filterDataString = JSON.stringify(newDbFilterData.filterData);
            const query = `INSERT INTO snb."SearchFilter" (id, "userId", "filterName", "filterData", "createdAt", "updatedAt") VALUES ('${newDbFilterData.id}', '${newDbFilterData.userId}', '${safeSearchName}', '${filterDataString}'::jsonb, NOW(), NOW()) RETURNING id, "filterName", "filterData", "createdAt";`;

            const result = await executeSupabaseSqlMcp(query, "handleSaveSearch") as DbSearchFilter[];
            // const savedDataFromDb = result[0]; // This would be used if MCP call was real
            // For now, we'll assume the call is made and then refetch to see the result.
            alert(`Save search "${searchName.trim()}" initiated.`);
            fetchSavedSearchesFromSupabase();
        } catch (error) {
            console.error('Error initiating save search:', error);
            alert('Failed to initiate save search.');
        }
    };

    const loadSavedSearch = (searchToLoad: ClientSavedSearch) => {
        const { filters } = searchToLoad;
        setLocation(filters.location);
        setMlsNumber(filters.mlsNumber);
        setPropertyType(filters.propertyType);
        setAdType(filters.adType);
        setPriceRange([...filters.priceRange]);
        setYearBuiltRange([...filters.yearBuiltRange]);
        setBedrooms(filters.bedrooms);
        setBathrooms(filters.bathrooms);
        setStoreys(filters.storeys);
        setParkingTypes([...filters.parkingTypes]);
        setExtraFeatures([...filters.extraFeatures]);
        alert(`Loaded search: ${searchToLoad.name}`);
        if (isMobile) setIsSidebarOpen(false);
    };

    const deleteSavedSearch = async (searchId: string) => {
        if (!isSignedIn || !user) {
            alert('You must be signed in to delete searches.');
            return;
        }
        if (!confirm('Are you sure you want to delete this saved search?')) {
            return;
        }
        try {
            const query = `DELETE FROM snb."SearchFilter" WHERE id = '${searchId}' AND "userId" = '${user.id}';`;
            await executeSupabaseSqlMcp(query, "deleteSavedSearch");
            alert('Delete search initiated.');
            setSavedSearches(prevSearches => prevSearches.filter(search => search.id !== searchId));
        } catch (error) {
            console.error('Error initiating delete search:', error);
            alert('Failed to initiate delete search.');
        }
    };

    const handleMapMove = useCallback((bounds: any) => {
        setMapBounds(bounds);
    }, []);

    const handleLikeProperty = (propertyId: string) => {
        if (!isSignedIn) {
            router.push('/sign-in?redirect=/properties');
            return;
        }
        const newLiked = new Set(likedProperties);
        if (newLiked.has(propertyId)) {
            newLiked.delete(propertyId);
        } else {
            newLiked.add(propertyId);
        }
        setLikedProperties(newLiked);
    };

    return (
        <div className="min-h-screen bg-background flex flex-col">
            {/* Header Section */}
            <div className="flex h-14 w-full items-center justify-between px-4 py-2 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="flex items-center gap-3">
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={toggleSidebar}
                        className={cn(
                            "h-9 w-9 rounded-lg transition-all duration-200",
                            isSidebarOpen
                                ? "bg-primary text-primary-foreground shadow-sm"
                                : "hover:bg-accent hover:text-accent-foreground"
                        )}
                    >
                        <SlidersHorizontal className="h-4 w-4" />
                    </Button>
                    <div className="hidden sm:flex items-center gap-2">
                        <Search className="h-4 w-4 text-primary" />
                        <h1 className="text-lg font-semibold">Property Search</h1>
                    </div>
                    <Badge variant="secondary" className="text-xs font-medium">
                        {properties.length} found
                    </Badge>
                </div>

                {/* View Mode Controls - Multi-select with minimum one required */}
                <div className="flex items-center gap-1">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewToggle('grid')}
                        className={cn(
                            "h-8 px-3 text-xs transition-all duration-200",
                            activeViews.has('grid')
                                ? "bg-primary/10 text-primary border border-primary/20 shadow-sm"
                                : "hover:bg-accent hover:text-accent-foreground"
                        )}
                    >
                        <Grid3X3 className="h-3.5 w-3.5 mr-1" />
                        <span className="hidden sm:inline">Grid</span>
                    </Button>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewToggle('list')}
                        className={cn(
                            "h-8 px-3 text-xs transition-all duration-200",
                            activeViews.has('list')
                                ? "bg-primary/10 text-primary border border-primary/20 shadow-sm"
                                : "hover:bg-accent hover:text-accent-foreground"
                        )}
                    >
                        <List className="h-3.5 w-3.5 mr-1" />
                        <span className="hidden sm:inline">List</span>
                    </Button>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewToggle('map')}
                        className={cn(
                            "h-8 px-3 text-xs transition-all duration-200",
                            activeViews.has('map')
                                ? "bg-primary/10 text-primary border border-primary/20 shadow-sm"
                                : "hover:bg-accent hover:text-accent-foreground"
                        )}
                    >
                        <Map className="h-3.5 w-3.5 mr-1" />
                        <span className="hidden sm:inline">Map</span>
                    </Button>
                </div>
            </div>

            {/* Main Content */}
            <div className="flex flex-1 overflow-hidden">
                {/* Filters Sheet */}
                <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
                    <SheetContent side="left" className="w-[90vw] sm:w-[400px] md:w-[480px] p-0 max-w-lg bg-background border-r border-border shadow-xl" style={{ backgroundColor: 'hsl(var(--background))', opacity: 1 }}>
                        <SheetHeader className="p-4 sm:p-6 border-b">
                            <SheetTitle className="text-lg font-semibold">Property Filters</SheetTitle>
                            <SheetDescription className="text-sm text-muted-foreground">
                                Refine your search to find the perfect property
                            </SheetDescription>
                        </SheetHeader>

                        <div className="flex-1 overflow-y-auto p-4 sm:p-6">
                            <div className="space-y-4 sm:space-y-6">
                                <div className="space-y-2">
                                    <Label htmlFor="location" className="text-sm font-medium">Location</Label>
                                    <Input
                                        id="location"
                                        value={location}
                                        onChange={(e) => setLocation(e.target.value)}
                                        placeholder="Enter city, neighborhood, or address"
                                        className="h-9"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="mlsNumber" className="text-sm font-medium">MLS Number</Label>
                                    <Input
                                        id="mlsNumber"
                                        value={mlsNumber}
                                        onChange={(e) => setMlsNumber(e.target.value)}
                                        placeholder="Enter MLS number"
                                        className="h-9"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="propertyType" className="text-sm font-medium">Property Type</Label>
                                    <Select value={propertyType} onValueChange={setPropertyType}>
                                        <SelectTrigger className="h-9">
                                            <SelectValue placeholder="Select property type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Types</SelectItem>
                                            <SelectItem value="house">House</SelectItem>
                                            <SelectItem value="condo">Condo</SelectItem>
                                            <SelectItem value="townhouse">Townhouse</SelectItem>
                                            <SelectItem value="land">Land</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="adType" className="text-sm font-medium">Listing Type</Label>
                                    <Select value={adType} onValueChange={setAdType}>
                                        <SelectTrigger className="h-9">
                                            <SelectValue placeholder="Select listing type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Types</SelectItem>
                                            <SelectItem value="sale">For Sale</SelectItem>
                                            <SelectItem value="rent">For Rent</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="grid grid-cols-2 gap-3">
                                    <div className="space-y-2">
                                        <Label className="text-sm font-medium">Bedrooms</Label>
                                        <Select value={bedrooms} onValueChange={setBedrooms}>
                                            <SelectTrigger className="h-9">
                                                <SelectValue placeholder="Any" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="any">Any</SelectItem>
                                                <SelectItem value="1">1+</SelectItem>
                                                <SelectItem value="2">2+</SelectItem>
                                                <SelectItem value="3">3+</SelectItem>
                                                <SelectItem value="4">4+</SelectItem>
                                                <SelectItem value="5">5+</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="space-y-2">
                                        <Label className="text-sm font-medium">Bathrooms</Label>
                                        <Select value={bathrooms} onValueChange={setBathrooms}>
                                            <SelectTrigger className="h-9">
                                                <SelectValue placeholder="Any" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="any">Any</SelectItem>
                                                <SelectItem value="1">1+</SelectItem>
                                                <SelectItem value="2">2+</SelectItem>
                                                <SelectItem value="3">3+</SelectItem>
                                                <SelectItem value="4">4+</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label className="text-sm font-medium">Price Range</Label>
                                    <div className="px-2">
                                        <Slider
                                            value={priceRange}
                                            onValueChange={setPriceRange}
                                            max={2000000}
                                            min={0}
                                            step={25000}
                                            className="w-full"
                                        />
                                        <div className="flex justify-between text-xs text-muted-foreground mt-1">
                                            <span>${priceRange[0].toLocaleString()}</span>
                                            <span>${priceRange[1].toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <SheetFooter className="p-4 sm:p-6 border-t bg-background/50">
                            <div className="flex gap-2 w-full">
                                <Button
                                    variant="outline"
                                    onClick={() => setIsSidebarOpen(false)}
                                    className="flex-1 h-9"
                                >
                                    Cancel
                                </Button>
                                <Button
                                    onClick={() => {
                                        handleSearch();
                                        if (isMobile) setIsSidebarOpen(false);
                                    }}
                                    className="flex-1 h-9"
                                >
                                    Apply Filters
                                </Button>
                            </div>
                        </SheetFooter>
                    </SheetContent>
                </Sheet>

                {/* Dynamic Layout based on active views */}
                <div className="flex w-full h-full">
                    {/* Map Section - Always show if map is active */}
                    {activeViews.has('map') && (
                        <div className={cn(
                            "h-full border-r border-border",
                            // Calculate map width based on active views
                            activeViews.size === 1 ? "w-full" : // Only map
                                activeViews.has('grid') && activeViews.has('list') ? "w-1/3" : // Map + Grid + List
                                    activeViews.has('grid') ? "w-1/2" : // Map + Grid
                                        activeViews.has('list') ? "w-2/3" : "w-1/2" // Map + List
                        )}>
                            <GoogleMap
                                properties={properties}
                                onMapMove={handleMapMove}
                                initialCenter={mapCenter}
                                searchRadius={searchRadius}
                                showRadius={showRadius}
                                onPropertyClick={(property) => console.log('Property clicked:', property)}
                            />
                        </div>
                    )}

                    {/* Properties Section - Show if grid or list is active */}
                    {(activeViews.has('grid') || activeViews.has('list')) && (
                        <div className={cn(
                            "h-full flex",
                            // Calculate properties section width
                            !activeViews.has('map') ? "w-full" : // No map
                                activeViews.has('grid') && activeViews.has('list') ? "w-2/3" : // Grid + List (with map)
                                    activeViews.has('grid') ? "w-1/2" : // Only Grid (with map)
                                        activeViews.has('list') ? "w-1/3" : "w-1/2" // Only List (with map)
                        )}>
                            {/* Grid Section */}
                            {activeViews.has('grid') && (
                                <div className={cn(
                                    "flex flex-col",
                                    activeViews.has('list') ? "w-1/2 border-r border-border" : "w-full"
                                )}>
                                    {/* Grid Header */}
                                    <div className="flex h-12 items-center justify-between px-4 border-b border-border bg-background/95">
                                        <span className="text-sm font-medium text-muted-foreground">
                                            Grid View ({properties.length})
                                        </span>
                                        <Select value={sort} onValueChange={setSort}>
                                            <SelectTrigger className="w-[140px] h-8">
                                                <SelectValue placeholder="Sort" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="price.asc">Price ↑</SelectItem>
                                                <SelectItem value="price.desc">Price ↓</SelectItem>
                                                <SelectItem value="created_at.desc">Newest</SelectItem>
                                                <SelectItem value="created_at.asc">Oldest</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Grid Content */}
                                    <div className="flex-1 overflow-y-auto p-4">
                                        {loading ? (
                                            <div className="flex items-center justify-center h-full">
                                                <div className="text-center space-y-2">
                                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                                                    <p className="text-sm text-muted-foreground">Loading...</p>
                                                </div>
                                            </div>
                                        ) : properties && properties.length > 0 ? (
                                            <div className="grid grid-cols-1 xl:grid-cols-2 gap-4">
                                                {properties.map((property) => (
                                                    <ShowcaseCard
                                                        key={property.id}
                                                        propertyId={property.id}
                                                        imageUrl={property.images && property.images.length > 0 ? property.images[0].url : undefined}
                                                        price={`$${property.price.toLocaleString()}`}
                                                        title={property.title || property.address}
                                                        location={property.address}
                                                        bedrooms={property.bedrooms}
                                                        bathrooms={property.bathrooms}
                                                        sqft={property.sqft || 0}
                                                        views={property.views || 0}
                                                        daysListed={property.daysListed || 0}
                                                        isLiked={likedProperties.has(property.id)}
                                                        onLikeClick={handleLikeProperty}
                                                        onDetailsLinkClick={handleCardDetailsLinkClick}
                                                        onClick={handleCardClick}
                                                    />
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="flex items-center justify-center h-full">
                                                <div className="text-center space-y-2">
                                                    <Grid3X3 className="h-12 w-12 text-muted-foreground/50 mx-auto" />
                                                    <p className="text-muted-foreground">No properties found</p>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* List Section */}
                            {activeViews.has('list') && (
                                <div className={cn(
                                    "flex flex-col",
                                    activeViews.has('grid') ? "w-1/2" : "w-full"
                                )}>
                                    {/* List Header */}
                                    <div className="flex h-12 items-center justify-between px-4 border-b border-border bg-background/95">
                                        <span className="text-sm font-medium text-muted-foreground">
                                            List View ({properties.length})
                                        </span>
                                        <Select value={sort} onValueChange={setSort}>
                                            <SelectTrigger className="w-[120px] h-8">
                                                <SelectValue placeholder="Sort" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="price.asc">Price ↑</SelectItem>
                                                <SelectItem value="price.desc">Price ↓</SelectItem>
                                                <SelectItem value="created_at.desc">Newest</SelectItem>
                                                <SelectItem value="created_at.asc">Oldest</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* List Content */}
                                    <div className="flex-1 overflow-y-auto p-3">
                                        {loading ? (
                                            <div className="flex items-center justify-center h-full">
                                                <div className="text-center space-y-2">
                                                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                                                    <p className="text-xs text-muted-foreground">Loading...</p>
                                                </div>
                                            </div>
                                        ) : properties && properties.length > 0 ? (
                                            <div className="space-y-3">
                                                {properties.map((property) => (
                                                    <ShowcaseCard
                                                        key={property.id}
                                                        propertyId={property.id}
                                                        imageUrl={property.images && property.images.length > 0 ? property.images[0].url : undefined}
                                                        price={`$${property.price.toLocaleString()}`}
                                                        title={property.title || property.address}
                                                        location={property.address}
                                                        bedrooms={property.bedrooms}
                                                        bathrooms={property.bathrooms}
                                                        sqft={property.sqft || 0}
                                                        views={property.views || 0}
                                                        daysListed={property.daysListed || 0}
                                                        isLiked={likedProperties.has(property.id)}
                                                        onLikeClick={handleLikeProperty}
                                                        onDetailsLinkClick={handleCardDetailsLinkClick}
                                                        onClick={handleCardClick}
                                                    />
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="flex items-center justify-center h-full">
                                                <div className="text-center space-y-2">
                                                    <List className="h-8 w-8 text-muted-foreground/50 mx-auto" />
                                                    <p className="text-xs text-muted-foreground">No properties found</p>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default PropertySearchClient;
