'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import GoogleMap from './GoogleMap';
import { Button } from '@/components/ui/button';
import { ShowcaseCard } from '@/components/ui/showcase-card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { useIsMobile } from '@/hooks/use-mobile';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Property } from '@/types/property';
import { Slider } from "@/components/ui/slider";
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { FilterBadge } from '@/components/ui/filter-badge';
import { SelectorChips } from '@/components/ui/selector-chips';
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from '@/components/ui/pagination';
import {
    SlidersHorizontal,
    Grid3X3,
    Map,
    MapPin,
    Home,
    DollarSign,
    Ruler,
    Bed,
    Bath,
    Car,
    Sparkles,
    X,
    ChevronDown
} from 'lucide-react';

import { cn } from '@/lib/utils';

interface ClientSavedSearch {
    id: string;
    name: string;
    filters: {
        location: string;
        mlsNumber: string;
        propertyType: string;
        adType: string;
        priceRange: number[];
        yearBuiltRange: number[];
        bedrooms: string;
        bathrooms: string;
        storeys: string;
        parkingTypes: string[];
        extraFeatures: string[];
    };
    createdAt?: string;
}

interface DbSearchFilter {
    id: string;
    userId: string;
    filterName: string;
    filterData: ClientSavedSearch['filters'];
    createdAt?: Date;
    updatedAt?: Date;
}

interface PropertySearchClientProps {
    initialProperties: Property[];
}

async function executeSupabaseSqlMcp(query: string, callingFunctionInfo: string): Promise<any[]> {
    console.info(
        `[${callingFunctionInfo}] INTENDS to execute SQL via MCP tool:\n` +
        `Query: ${query}\n` +
        `The AI (Cline) should now provide a <use_mcp_tool> instruction with:\n` +
        `Server: github.com/supabase-community/supabase-mcp\n` +
        `Tool: execute_sql\n` +
        `Arguments: { "project_id": "yfznlsisxsnymkvydzha", "query": (the SQL query above) }`
    );
    console.warn("executeSupabaseSqlMcp: If no MCP call is triggered by the AI, this will use localStorage fallback.");
    if (typeof window !== 'undefined' && window.localStorage) {
        const userIdMatch = query.match(/'(user_[^']+)'/);
        const userId = userIdMatch ? userIdMatch[1] : null;
        const storageKey = userId ? `savedSearches_${userId}_supabase_placeholder` : `savedSearches_unknownUser_supabase_placeholder`;

        if (query.startsWith('SELECT')) {
            const stored = localStorage.getItem(storageKey);
            console.log(`[LocalStorage Fallback for SELECT from ${storageKey}]:`, stored ? JSON.parse(stored) : []);
            return stored ? JSON.parse(stored) : [];
        } else if (query.startsWith('INSERT')) {
            const valuesMatch = query.match(/VALUES \('([^']*)', '([^']*)', '([^']*)', '([^']*)'::jsonb, NOW\(\), NOW\(\)\)/);
            if (valuesMatch) {
                const newRecord: DbSearchFilter = {
                    id: valuesMatch[1], userId: valuesMatch[2], filterName: valuesMatch[3].replace(/''/g, "'"),
                    filterData: JSON.parse(valuesMatch[4]), createdAt: new Date(), updatedAt: new Date()
                };
                const currentSaved = JSON.parse(localStorage.getItem(storageKey) || '[]') as DbSearchFilter[];
                currentSaved.push(newRecord);
                localStorage.setItem(storageKey, JSON.stringify(currentSaved));
                console.log(`[LocalStorage Fallback for INSERT to ${storageKey}]:`, newRecord);
                return [{ id: newRecord.id, filterName: newRecord.filterName, filterData: newRecord.filterData, createdAt: newRecord.createdAt.toISOString() }];
            }
        } else if (query.startsWith('DELETE')) {
            const idMatch = query.match(/id = '([^']+)'/);
            const userIdForDeleteMatch = query.match(/"userId" = '([^']+)'/);
            if (idMatch && idMatch[1] && userIdForDeleteMatch && userIdForDeleteMatch[1]) {
                const idToDelete = idMatch[1];
                let currentSaved = JSON.parse(localStorage.getItem(storageKey) || '[]') as DbSearchFilter[];
                currentSaved = currentSaved.filter(s => s.id !== idToDelete);
                localStorage.setItem(storageKey, JSON.stringify(currentSaved));
                console.log(`[LocalStorage Fallback for DELETE from ${storageKey}]: id ${idToDelete}`);
                return [];
            }
        }
    }
    console.error("executeSupabaseSqlMcp: LocalStorage fallback failed or query type not supported.");
    return Promise.resolve([]);
}


const PropertySearchClient: React.FC<PropertySearchClientProps> = ({ initialProperties }) => {
    const router = useRouter();
    const { user, isSignedIn } = useUser();
    const searchParams = useSearchParams();
    const isMobile = useIsMobile();
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [properties, setProperties] = useState<Property[]>(initialProperties);
    const [loading, setLoading] = useState(false);

    const [location, setLocation] = useState(searchParams.get('location') || '');
    const [mlsNumber, setMlsNumber] = useState(searchParams.get('mlsNumber') || '');
    const [propertyType, setPropertyType] = useState(searchParams.get('propertyType') || 'all');
    const [adType, setAdType] = useState(searchParams.get('adType') || 'all');
    const [priceRange, setPriceRange] = useState([
        parseInt(searchParams.get('minPrice') || '0'),
        parseInt(searchParams.get('maxPrice') || '2000000')
    ]);
    const [yearBuiltRange, setYearBuiltRange] = useState([
        parseInt(searchParams.get('minYear') || '1900'),
        parseInt(searchParams.get('maxYear') || '2024')
    ]);
    const [bedrooms, setBedrooms] = useState(searchParams.get('bedrooms') || 'any');
    const [bathrooms, setBathrooms] = useState(searchParams.get('bathrooms') || 'any');
    const [storeys, setStoreys] = useState(searchParams.get('storeys') || '');
    const [parkingTypes, setParkingTypes] = useState<string[]>(
        searchParams.get('parkingTypes')?.split(',').filter(Boolean) || []
    );
    const [extraFeatures, setExtraFeatures] = useState<string[]>(
        searchParams.get('extraFeatures')?.split(',').filter(Boolean) || []
    );
    const [sort, setSort] = useState(searchParams.get('sort') || 'created_at.desc');
    const [mapBounds, setMapBounds] = useState<any>(null);
    const [activeViews, setActiveViews] = useState<Set<'grid' | 'map'>>(new Set(['map', 'grid']));
    const [likedProperties, setLikedProperties] = useState<Set<string>>(new Set());
    const [isFiltersVisible, setIsFiltersVisible] = useState(false);
    const [distanceRange, setDistanceRange] = useState([5]);
    const [showAdvanced, setShowAdvanced] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const propertiesPerPage = 10;
    const [searchRadius, setSearchRadius] = useState(10);
    const [showRadius, setShowRadius] = useState(false);
    const [mapCenter, setMapCenter] = useState({ lat: 43.6532, lng: -79.3832 });

    const [savedSearches, setSavedSearches] = useState<ClientSavedSearch[]>([]);
    const [isFetchingSavedSearches, setIsFetchingSavedSearches] = useState(false);

    const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

    const toggleFilters = () => setIsFiltersVisible(!isFiltersVisible);

    const handleViewToggle = (view: 'grid' | 'map') => {
        const newActiveViews = new Set(activeViews);

        if (newActiveViews.has(view)) {
            // Only allow deselection if there's more than one view active
            if (newActiveViews.size > 1) {
                newActiveViews.delete(view);
            }
        } else {
            newActiveViews.add(view);
        }

        setActiveViews(newActiveViews);
    };

    const handleCardClick = (propertyId: string) => {
        router.push(`/properties/${propertyId}`);
    };

    const handleCardDetailsLinkClick = (propertyId: string) => {
        window.open(`/properties/${propertyId}`, '_blank');
    };

    const fetchSavedSearchesFromSupabase = useCallback(async () => {
        console.log("[PropertySearchClient] Attempting to call fetchSavedSearchesFromSupabase. User signed in:", isSignedIn, "User object:", user);
        if (!isSignedIn || !user) {
            setSavedSearches([]);
            setIsFetchingSavedSearches(false); // Ensure loading state is reset
            return;
        }
        setIsFetchingSavedSearches(true);
        try {
            const query = `SELECT id, "filterName", "filterData", "createdAt" FROM snb."SearchFilter" WHERE "userId" = '${user.id}' ORDER BY "createdAt" DESC;`;
            const dbResults = await executeSupabaseSqlMcp(query, "fetchSavedSearchesFromSupabase") as DbSearchFilter[];

            setSavedSearches(dbResults.map((item: DbSearchFilter) => ({
                id: item.id,
                name: item.filterName,
                filters: item.filterData,
                createdAt: item.createdAt ? new Date(item.createdAt).toISOString() : undefined,
            })).sort((a, b) => {
                const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
                const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
                return dateB - dateA;
            }));

        } catch (e) {
            console.error("Failed to fetch saved searches from Supabase:", e);
        } finally {
            setIsFetchingSavedSearches(false);
        }
    }, [isSignedIn, user]);

    useEffect(() => {
        fetchSavedSearchesFromSupabase();
    }, [fetchSavedSearchesFromSupabase]);

    const parkingOptions = [
        'Assigned Parking', 'Attached Garage', 'Carport', 'Covered Parking',
        'Detached Garage', 'Driveway', 'Indoor Parking', 'Other Parking',
        'Parking Lot', 'Parking Pad', 'Parking Stall', 'Street Parking',
        'Underground', 'Visitor Parking'
    ];

    const extraFeaturesOptions = [
        'Wheelchair Accessible', 'Hot Tub', 'Guest Suite', 'MLS® System',
        'Fireplace', 'Pool', 'Legal Suite', 'Wood Stove', 'Games Room',
        'Private Entrance', 'A/C', 'Home Theatre', 'Open House',
        'Jacuzzi', 'Walkout', 'Virtual Tour/Video'
    ];

    const fetchProperties = useCallback(async () => {
        setLoading(true);
        try {
            const params = new URLSearchParams();
            if (location && location.trim()) params.set('location', location.trim());
            if (mlsNumber && mlsNumber.trim()) params.set('mlsNumber', mlsNumber.trim());
            if (propertyType && propertyType !== 'all') params.set('propertyType', propertyType);
            if (adType && adType !== 'all') params.set('adType', adType);
            if (priceRange[0] > 0) params.set('minPrice', priceRange[0].toString());
            if (priceRange[1] < 2000000) params.set('maxPrice', priceRange[1].toString());
            if (yearBuiltRange[0] > 1900) params.set('minYear', yearBuiltRange[0].toString());
            if (yearBuiltRange[1] < 2024) params.set('maxYear', yearBuiltRange[1].toString());
            if (bedrooms && bedrooms !== 'any') params.set('bedrooms', bedrooms);
            if (bathrooms && bathrooms !== 'any') params.set('bathrooms', bathrooms);
            if (storeys) params.set('storeys', storeys);
            if (parkingTypes.length > 0) params.set('parkingTypes', parkingTypes.join(','));
            if (extraFeatures.length > 0) params.set('extraFeatures', extraFeatures.join(','));
            if (sort) params.set('sort', sort);
            if (mapBounds) {
                params.set('northEastLat', mapBounds.getNorthEast().lat.toString());
                params.set('northEastLng', mapBounds.getNorthEast().lng.toString());
                params.set('southWestLat', mapBounds.getSouthWest().lat.toString());
                params.set('southWestLng', mapBounds.getSouthWest().lng.toString());
            }
            const queryString = params.toString();
            const apiUrl = queryString ? `/api/properties/search?${queryString}` : '/api/properties/search';
            const response = await fetch(apiUrl);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            setProperties(data);
            const newUrl = queryString ? `/properties?${queryString}` : '/properties';
            router.push(newUrl, { scroll: false });
        } catch (error) {
            console.error('Error fetching properties:', error);
            setProperties([]);
        } finally {
            setLoading(false);
        }
    }, [location, mlsNumber, propertyType, adType, priceRange, yearBuiltRange, bedrooms, bathrooms, storeys, parkingTypes, extraFeatures, sort, mapBounds, router]);

    useEffect(() => {
        if (initialProperties.length === 0) {
            fetchProperties();
        }
    }, []);

    const handleSearch = () => {
        fetchProperties();
    };

    const handleSaveSearch = async () => {
        if (!isSignedIn || !user) {
            router.push('/sign-in?redirect=/properties');
            return;
        }
        const searchName = prompt('Enter a name for this search:');
        if (!searchName || searchName.trim() === '') {
            alert('Search name cannot be empty.');
            return;
        }
        const currentFiltersForDB: ClientSavedSearch['filters'] = {
            location, mlsNumber, propertyType, adType, priceRange,
            yearBuiltRange, bedrooms, bathrooms, storeys, parkingTypes, extraFeatures,
        };
        const newSearchId = crypto.randomUUID();
        const newDbFilterData: Omit<DbSearchFilter, 'createdAt' | 'updatedAt'> = {
            id: newSearchId, userId: user.id, filterName: searchName.trim(), filterData: currentFiltersForDB,
        };
        try {
            const safeSearchName = newDbFilterData.filterName.replace(/'/g, "''");
            const filterDataString = JSON.stringify(newDbFilterData.filterData);
            const query = `INSERT INTO snb."SearchFilter" (id, "userId", "filterName", "filterData", "createdAt", "updatedAt") VALUES ('${newDbFilterData.id}', '${newDbFilterData.userId}', '${safeSearchName}', '${filterDataString}'::jsonb, NOW(), NOW()) RETURNING id, "filterName", "filterData", "createdAt";`;

            const result = await executeSupabaseSqlMcp(query, "handleSaveSearch") as DbSearchFilter[];
            // const savedDataFromDb = result[0]; // This would be used if MCP call was real
            // For now, we'll assume the call is made and then refetch to see the result.
            alert(`Save search "${searchName.trim()}" initiated.`);
            fetchSavedSearchesFromSupabase();
        } catch (error) {
            console.error('Error initiating save search:', error);
            alert('Failed to initiate save search.');
        }
    };

    const loadSavedSearch = (searchToLoad: ClientSavedSearch) => {
        const { filters } = searchToLoad;
        setLocation(filters.location);
        setMlsNumber(filters.mlsNumber);
        setPropertyType(filters.propertyType);
        setAdType(filters.adType);
        setPriceRange([...filters.priceRange]);
        setYearBuiltRange([...filters.yearBuiltRange]);
        setBedrooms(filters.bedrooms);
        setBathrooms(filters.bathrooms);
        setStoreys(filters.storeys);
        setParkingTypes([...filters.parkingTypes]);
        setExtraFeatures([...filters.extraFeatures]);
        alert(`Loaded search: ${searchToLoad.name}`);
        if (isMobile) setIsSidebarOpen(false);
    };

    const deleteSavedSearch = async (searchId: string) => {
        if (!isSignedIn || !user) {
            alert('You must be signed in to delete searches.');
            return;
        }
        if (!confirm('Are you sure you want to delete this saved search?')) {
            return;
        }
        try {
            const query = `DELETE FROM snb."SearchFilter" WHERE id = '${searchId}' AND "userId" = '${user.id}';`;
            await executeSupabaseSqlMcp(query, "deleteSavedSearch");
            alert('Delete search initiated.');
            setSavedSearches(prevSearches => prevSearches.filter(search => search.id !== searchId));
        } catch (error) {
            console.error('Error initiating delete search:', error);
            alert('Failed to initiate delete search.');
        }
    };

    const handleMapMove = useCallback((bounds: any) => {
        setMapBounds(bounds);
    }, []);

    const handleLikeProperty = (propertyId: string) => {
        if (!isSignedIn) {
            router.push('/sign-in?redirect=/properties');
            return;
        }
        const newLiked = new Set(likedProperties);
        if (newLiked.has(propertyId)) {
            newLiked.delete(propertyId);
        } else {
            newLiked.add(propertyId);
        }
        setLikedProperties(newLiked);
    };

    return (
        <div className="min-h-screen bg-background flex flex-col">
            {/* Header Section */}
            <div className="flex h-14 w-full items-center justify-between px-4 py-2 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="flex items-center gap-3">
                    <Button
                        onClick={toggleFilters}
                        className={cn(
                            "flex items-center gap-2 h-10 px-4 rounded-lg transition-all duration-200 border-2",
                            isFiltersVisible
                                ? "bg-primary text-primary-foreground border-primary shadow-lg"
                                : "bg-background text-foreground border-border hover:bg-accent hover:text-accent-foreground hover:border-primary/50"
                        )}
                    >
                        <SlidersHorizontal className="h-4 w-4" />
                        <span className="text-sm font-medium">Search Properties</span>
                    </Button>
                    <Badge variant="secondary" className="text-xs font-medium">
                        {properties.length} found
                    </Badge>
                </div>

                {/* View Mode Controls - Map and Grid toggle */}
                <div className="flex items-center gap-1">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewToggle('map')}
                        className={cn(
                            "h-8 px-3 text-xs transition-all duration-200",
                            activeViews.has('map')
                                ? "bg-primary/10 text-primary border border-primary/20 shadow-sm"
                                : "hover:bg-accent hover:text-accent-foreground"
                        )}
                    >
                        <Map className="h-3.5 w-3.5 mr-1" />
                        <span className="hidden sm:inline">Map</span>
                    </Button>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewToggle('grid')}
                        className={cn(
                            "h-8 px-3 text-xs transition-all duration-200",
                            activeViews.has('grid')
                                ? "bg-primary/10 text-primary border border-primary/20 shadow-sm"
                                : "hover:bg-accent hover:text-accent-foreground"
                        )}
                    >
                        <Grid3X3 className="h-3.5 w-3.5 mr-1" />
                        <span className="hidden sm:inline">Grid</span>
                    </Button>
                </div>
            </div>

            {/* Modern Filters Section */}
            {isFiltersVisible && (
                <div className="w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <div className="p-2 space-y-2">
                        {/* Active Filters Display */}
                        <div className="flex flex-wrap gap-2">
                            {location && (
                                <FilterBadge
                                    label="Location"
                                    value={location}
                                    onRemove={() => setLocation('')}
                                />
                            )}
                            {propertyType !== 'all' && (
                                <FilterBadge
                                    label="Type"
                                    value={propertyType}
                                    onRemove={() => setPropertyType('all')}
                                />
                            )}
                            {adType !== 'all' && (
                                <FilterBadge
                                    label="Listing"
                                    value={adType}
                                    onRemove={() => setAdType('all')}
                                />
                            )}
                            {bedrooms !== 'any' && (
                                <FilterBadge
                                    label="Bedrooms"
                                    value={`${bedrooms}+`}
                                    onRemove={() => setBedrooms('any')}
                                />
                            )}
                            {bathrooms !== 'any' && (
                                <FilterBadge
                                    label="Bathrooms"
                                    value={`${bathrooms}+`}
                                    onRemove={() => setBathrooms('any')}
                                />
                            )}
                            {storeys && (
                                <FilterBadge
                                    label="Storeys"
                                    value={storeys}
                                    onRemove={() => setStoreys('')}
                                />
                            )}
                            {(priceRange[0] > 0 || priceRange[1] < 2000000) && (
                                <FilterBadge
                                    label="Price"
                                    value={`$${priceRange[0].toLocaleString()} - $${priceRange[1].toLocaleString()}`}
                                    onRemove={() => setPriceRange([0, 2000000])}
                                />
                            )}
                            {(yearBuiltRange[0] > 1900 || yearBuiltRange[1] < 2024) && (
                                <FilterBadge
                                    label="Year Built"
                                    value={`${yearBuiltRange[0]} - ${yearBuiltRange[1]}`}
                                    onRemove={() => setYearBuiltRange([1900, 2024])}
                                />
                            )}
                            {distanceRange[0] !== 5 && (
                                <FilterBadge
                                    label="Distance"
                                    value={`${distanceRange[0]} km`}
                                    onRemove={() => setDistanceRange([5])}
                                />
                            )}
                            {parkingTypes.length > 0 && (
                                <FilterBadge
                                    label="Parking"
                                    value={`${parkingTypes.length} selected`}
                                    onRemove={() => setParkingTypes([])}
                                />
                            )}
                            {extraFeatures.length > 0 && (
                                <FilterBadge
                                    label="Features"
                                    value={`${extraFeatures.length} selected`}
                                    onRemove={() => setExtraFeatures([])}
                                />
                            )}
                        </div>

                        {/* Filter Controls Grid */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2">
                            {/* Location Search */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <MapPin className="h-4 w-4 text-primary" />
                                    <Label className="text-sm font-medium">Location</Label>
                                </div>
                                <Input
                                    value={location}
                                    onChange={(e) => setLocation(e.target.value)}
                                    placeholder="City, neighborhood, or address"
                                    className="h-9"
                                />
                            </div>

                            {/* Property Type */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Home className="h-4 w-4 text-primary" />
                                    <Label className="text-sm font-medium">Property Type</Label>
                                </div>
                                <SelectorChips
                                    options={['House', 'Condo', 'Townhouse', 'Land']}
                                    onChange={(selected) => {
                                        if (selected.length === 0) {
                                            setPropertyType('all');
                                        } else {
                                            setPropertyType(selected[0].toLowerCase());
                                        }
                                    }}
                                />
                            </div>

                            {/* Ad Type */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Home className="h-4 w-4 text-primary" />
                                    <Label className="text-sm font-medium">Ad Type</Label>
                                </div>
                                <SelectorChips
                                    options={['Sale', 'Rent', 'Lease']}
                                    onChange={(selected) => {
                                        if (selected.length === 0) {
                                            setAdType('all');
                                        } else {
                                            setAdType(selected[0].toLowerCase());
                                        }
                                    }}
                                />
                            </div>

                            {/* Price Range */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <DollarSign className="h-4 w-4 text-primary" />
                                    <Label className="text-sm font-medium">Price Range</Label>
                                </div>
                                <div className="p-3 border-2 border-border rounded-xl bg-background flex flex-col items-center justify-center">
                                    <Slider
                                        value={priceRange}
                                        onValueChange={setPriceRange}
                                        max={2000000}
                                        min={0}
                                        step={25000}
                                        className="w-full [&>span:first-child]:h-2 [&>span:first-child]:bg-primary/20 [&>span:last-child]:bg-primary [&>span:last-child]:border-2 [&>span:last-child]:border-primary"
                                    />
                                    <div className="flex justify-between text-xs text-muted-foreground mt-2 w-full">
                                        <span>${priceRange[0].toLocaleString()}</span>
                                        <span>${priceRange[1].toLocaleString()}</span>
                                    </div>
                                </div>
                            </div>

                            {/* Year Built */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Home className="h-4 w-4 text-primary" />
                                    <Label className="text-sm font-medium">Year Built</Label>
                                </div>
                                <div className="p-3 border-2 border-border rounded-xl bg-background flex flex-col items-center justify-center">
                                    <Slider
                                        value={yearBuiltRange}
                                        onValueChange={setYearBuiltRange}
                                        max={2024}
                                        min={1900}
                                        step={1}
                                        className="w-full [&>span:first-child]:h-2 [&>span:first-child]:bg-primary/20 [&>span:last-child]:bg-primary [&>span:last-child]:border-2 [&>span:last-child]:border-primary"
                                    />
                                    <div className="flex justify-between text-xs text-muted-foreground mt-2 w-full">
                                        <span>{yearBuiltRange[0]}</span>
                                        <span>{yearBuiltRange[1]}</span>
                                    </div>
                                </div>
                            </div>

                            {/* Distance */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Ruler className="h-4 w-4 text-primary" />
                                    <Label className="text-sm font-medium">Distance</Label>
                                </div>
                                <div className="p-3 border-2 border-border rounded-xl bg-background flex flex-col items-center justify-center">
                                    <Slider
                                        value={distanceRange}
                                        onValueChange={setDistanceRange}
                                        max={50}
                                        min={1}
                                        step={1}
                                        className="w-full [&>span:first-child]:h-2 [&>span:first-child]:bg-primary/20 [&>span:last-child]:bg-primary [&>span:last-child]:border-2 [&>span:last-child]:border-primary"
                                    />
                                    <div className="text-center text-xs text-muted-foreground mt-2">
                                        {distanceRange[0]} km radius
                                    </div>
                                </div>
                            </div>

                            {/* Bedrooms */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Bed className="h-4 w-4 text-primary" />
                                    <Label className="text-sm font-medium">Bedrooms</Label>
                                </div>
                                <SelectorChips
                                    options={['Any', '1+', '2+', '3+', '4+', '5+']}
                                    onChange={(selected) => {
                                        if (selected.length === 0 || selected.includes('Any')) {
                                            setBedrooms('any');
                                        } else {
                                            setBedrooms(selected[0].replace('+', ''));
                                        }
                                    }}
                                />
                            </div>

                            {/* Bathrooms */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Bath className="h-4 w-4 text-primary" />
                                    <Label className="text-sm font-medium">Bathrooms</Label>
                                </div>
                                <SelectorChips
                                    options={['Any', '1+', '2+', '3+', '4+']}
                                    onChange={(selected) => {
                                        if (selected.length === 0 || selected.includes('Any')) {
                                            setBathrooms('any');
                                        } else {
                                            setBathrooms(selected[0].replace('+', ''));
                                        }
                                    }}
                                />
                            </div>

                            {/* Storeys */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Home className="h-4 w-4 text-primary" />
                                    <Label className="text-sm font-medium">Storeys</Label>
                                </div>
                                <SelectorChips
                                    options={['Any', '1', '2', '3', '4+']}
                                    onChange={(selected) => {
                                        if (selected.length === 0 || selected.includes('Any')) {
                                            setStoreys('');
                                        } else {
                                            setStoreys(selected[0]);
                                        }
                                    }}
                                />
                            </div>

                            {/* Parking Types */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Car className="h-4 w-4 text-primary" />
                                    <Label className="text-sm font-medium">Parking</Label>
                                </div>
                                <SelectorChips
                                    options={[
                                        'Assigned Parking', 'Attached Garage', 'Carport', 'Covered Parking',
                                        'Detached Garage', 'Driveway', 'Indoor Parking', 'Other Parking',
                                        'Parking Lot', 'Parking Pad', 'Parking Stall', 'Street Parking',
                                        'Underground', 'Visitor Parking'
                                    ]}
                                    onChange={(selected) => setParkingTypes(selected)}
                                />
                            </div>

                            {/* Extra Features */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Sparkles className="h-4 w-4 text-primary" />
                                    <Label className="text-sm font-medium">Features</Label>
                                </div>
                                <SelectorChips
                                    options={[
                                        'Wheelchair Accessible', 'Hot Tub', 'Guest Suite', 'MLS® System',
                                        'Fireplace', 'Pool', 'Legal Suite', 'Wood Stove', 'Games Room',
                                        'Private Entrance', 'A/C', 'Home Theatre', 'Open House',
                                        'Jacuzzi', 'Walkout', 'Virtual Tour/Video'
                                    ]}
                                    onChange={(selected) => setExtraFeatures(selected)}
                                />
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-between items-center pt-2 border-t border-border">
                            <Button
                                variant="outline"
                                onClick={() => {
                                    setLocation('');
                                    setPropertyType('all');
                                    setAdType('all');
                                    setBedrooms('any');
                                    setBathrooms('any');
                                    setStoreys('');
                                    setPriceRange([0, 2000000]);
                                    setYearBuiltRange([1900, 2024]);
                                    setDistanceRange([5]);
                                    setParkingTypes([]);
                                    setExtraFeatures([]);
                                }}
                                className="h-9"
                            >
                                <X className="h-4 w-4 mr-2" />
                                Clear All
                            </Button>
                            <div className="flex gap-2">
                                <Button
                                    variant="outline"
                                    onClick={() => setIsFiltersVisible(false)}
                                    className="h-9"
                                >
                                    Cancel
                                </Button>
                                <Button
                                    onClick={() => {
                                        handleSearch();
                                        setIsFiltersVisible(false);
                                    }}
                                    className="h-9"
                                >
                                    Apply Filters
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Main Content */}
            <div className="flex flex-1 overflow-y-auto">

                {/* Dynamic Layout based on active views */}
                <div className="flex w-full h-full">
                    {/* Map Section - Show if map is active */}
                    {activeViews.has('map') && (
                        <div className={cn(
                            "h-full",
                            // Calculate map width based on active views and filter state
                            !activeViews.has('grid') ? "w-full" : // Only map when grid is deselected
                                isFiltersVisible ? "w-2/3 border-r-2 border-border" : "w-1/2 border-r-2 border-border"
                        )}>
                            <GoogleMap
                                properties={properties}
                                onMapMove={handleMapMove}
                                initialCenter={mapCenter}
                                searchRadius={searchRadius}
                                showRadius={showRadius}
                                onPropertyClick={(property) => console.log('Property clicked:', property)}
                            />
                        </div>
                    )}

                    {/* Grid Section - Show if grid is active */}
                    {activeViews.has('grid') && (
                        <div className={cn(
                            "h-full flex flex-col",
                            // Calculate grid width based on active views and filter state
                            !activeViews.has('map') ? "w-full" : // No map
                                isFiltersVisible ? "w-1/3" : "w-1/2" // With map
                        )}>
                            {/* Grid Header */}
                            <div className="flex h-12 items-center justify-between px-4 border-b border-border bg-background/95">
                                <span className="text-sm font-medium text-muted-foreground">
                                    {isFiltersVisible ? 'List View' : 'Grid View'} ({properties.length})
                                </span>
                                <Select value={sort} onValueChange={setSort}>
                                    <SelectTrigger className={cn(
                                        "h-8",
                                        isFiltersVisible ? "w-[100px]" : "w-[140px]"
                                    )}>
                                        <SelectValue placeholder="Sort" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="price.asc">Price ↑</SelectItem>
                                        <SelectItem value="price.desc">Price ↓</SelectItem>
                                        <SelectItem value="created_at.desc">Newest</SelectItem>
                                        <SelectItem value="created_at.asc">Oldest</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            {/* Grid Content - Dynamic layout based on filter state */}
                            <div className={cn(
                                "flex-1 overflow-y-auto",
                                isFiltersVisible ? "p-2" : "p-4"
                            )}>
                                {loading ? (
                                    <div className="flex items-center justify-center h-full">
                                        <div className="text-center space-y-2">
                                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                                            <p className="text-sm text-muted-foreground">Loading...</p>
                                        </div>
                                    </div>
                                ) : properties && properties.length > 0 ? (
                                    <div className={cn(
                                        // Dynamic grid based on filter state and available space
                                        isFiltersVisible
                                            ? "space-y-0.5" // List view when filters open - single column with minimal spacing
                                            : activeViews.has('map')
                                                ? "grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-0.5" // Grid with map - responsive columns
                                                : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-0.5" // Full width grid - more columns
                                    )}>
                                        {properties.map((property) => (
                                            <ShowcaseCard
                                                key={property.id}
                                                propertyId={property.id}
                                                imageUrl={property.images && property.images.length > 0 ? property.images[0].url : undefined}
                                                price={`$${property.price.toLocaleString()}`}
                                                title={property.title || property.address}
                                                location={property.address}
                                                bedrooms={property.bedrooms}
                                                bathrooms={property.bathrooms}
                                                sqft={property.sqft || 0}
                                                views={property.views || 0}
                                                daysListed={property.daysListed || 0}
                                                isLiked={likedProperties.has(property.id)}
                                                onLikeClick={handleLikeProperty}
                                                onDetailsLinkClick={handleCardDetailsLinkClick}
                                                onClick={handleCardClick}
                                            />
                                        ))}
                                    </div>
                                ) : (
                                    <div className="flex items-center justify-center h-full">
                                        <div className="text-center space-y-2">
                                            <Grid3X3 className="h-12 w-12 text-muted-foreground/50 mx-auto" />
                                            <p className="text-muted-foreground">No properties found</p>
                                            <p className="text-sm text-muted-foreground/70">Try adjusting your search criteria</p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                </div>
            </div>
        </div>
    );
};

export default PropertySearchClient;
