# This is an example .env file. Copy it to .env and fill in your values.
# Lines starting with # are comments.

# === Main LLM Configuration (MCP_LLM_*) ===
# Select the primary LLM provider
# Options: openai, azure_openai, anthropic, google, mistral, ollama, deepseek, openrouter, alibaba, moonshot, unbound
MCP_LLM_PROVIDER=google
# Specify the model name for the selected provider
MCP_LLM_MODEL_NAME=gemini-2.5-flash-preview-04-17
# LLM temperature (0.0-2.0). Controls randomness.
MCP_LLM_TEMPERATURE=0.0
# Optional: Generic override for the LLM provider's base URL
# MCP_LLM_BASE_URL=
# Optional: Generic override for the LLM provider's API key. Takes precedence over provider-specific keys.
# MCP_LLM_API_KEY=

# --- Provider Specific API Keys (MCP_LLM_*) ---
# Required unless using Ollama locally without auth or generic MCP_LLM_API_KEY is set
# MCP_LLM_OPENAI_API_KEY=YOUR_OPENAI_API_KEY
# MCP_LLM_ANTHROPIC_API_KEY=YOUR_ANTHROPIC_API_KEY
# MCP_LLM_GOOGLE_API_KEY=YOUR_GOOGLE_API_KEY
# MCP_LLM_AZURE_OPENAI_API_KEY=YOUR_AZURE_OPENAI_API_KEY
# MCP_LLM_DEEPSEEK_API_KEY=YOUR_DEEPSEEK_API_KEY
# MCP_LLM_MISTRAL_API_KEY=YOUR_MISTRAL_API_KEY
# MCP_LLM_OPENROUTER_API_KEY=YOUR_OPENROUTER_API_KEY
# MCP_LLM_ALIBABA_API_KEY=YOUR_ALIBABA_API_KEY
# MCP_LLM_MOONSHOT_API_KEY=YOUR_MOONSHOT_API_KEY
# MCP_LLM_UNBOUND_API_KEY=YOUR_UNBOUND_API_KEY

# --- Provider Specific Endpoints (MCP_LLM_*) ---
# Optional: Override default API endpoints.
# MCP_LLM_OPENAI_ENDPOINT=https://api.openai.com/v1
# MCP_LLM_ANTHROPIC_ENDPOINT=https://api.anthropic.com
# MCP_LLM_AZURE_OPENAI_ENDPOINT=YOUR_AZURE_ENDPOINT # Required if using Azure, e.g., https://your-resource.openai.azure.com/
# MCP_LLM_AZURE_OPENAI_API_VERSION=2025-01-01-preview
# MCP_LLM_DEEPSEEK_ENDPOINT=https://api.deepseek.com
# MCP_LLM_MISTRAL_ENDPOINT=https://api.mistral.ai/v1
# MCP_LLM_OLLAMA_ENDPOINT=http://localhost:11434
# MCP_LLM_OPENROUTER_ENDPOINT=https://openrouter.ai/api/v1
# MCP_LLM_ALIBABA_ENDPOINT=https://dashscope.aliyuncs.com/compatible-mode/v1
# MCP_LLM_MOONSHOT_ENDPOINT=https://api.moonshot.cn/v1
# MCP_LLM_UNBOUND_ENDPOINT=https://api.getunbound.ai

# --- Ollama Specific (MCP_LLM_*) ---
# MCP_LLM_OLLAMA_NUM_CTX=32000
# MCP_LLM_OLLAMA_NUM_PREDICT=1024

# === Planner LLM Configuration (Optional, MCP_LLM_PLANNER_*) ===
# If you want to use a different LLM for planning tasks within agents.
# Defaults to main LLM settings if not specified.
# MCP_LLM_PLANNER_PROVIDER=
# MCP_LLM_PLANNER_MODEL_NAME=
# MCP_LLM_PLANNER_TEMPERATURE=
# MCP_LLM_PLANNER_BASE_URL=
# MCP_LLM_PLANNER_API_KEY= # Generic planner API key, or use provider-specific below
# MCP_LLM_PLANNER_OPENAI_API_KEY=
# ... (similar provider-specific keys and endpoints for planner if needed)

# === Browser Configuration (MCP_BROWSER_*) ===
# General browser headless mode (true/false)
MCP_BROWSER_HEADLESS=false
# General browser disable security features (use cautiously) (true/false)
MCP_BROWSER_DISABLE_SECURITY=false
# Optional: Path to Chrome/Chromium executable
# MCP_BROWSER_BINARY_PATH=/usr/bin/chromium-browser
# Optional: Path to Chrome user data directory (for persistent sessions)
# MCP_BROWSER_USER_DATA_DIR=~/.config/google-chrome/Profile 1
MCP_BROWSER_WINDOW_WIDTH=1280
MCP_BROWSER_WINDOW_HEIGHT=1080
# Set to true to connect to user's browser via MCP_BROWSER_CDP_URL
MCP_BROWSER_USE_OWN_BROWSER=false
# Optional: Connect to existing Chrome via DevTools Protocol URL. Required if MCP_BROWSER_USE_OWN_BROWSER=true.
# MCP_BROWSER_CDP_URL=http://localhost:9222
# MCP_BROWSER_WSS_URL= # Optional: WSS URL if CDP URL is not sufficient
# Keep browser managed by server open between MCP tool calls (if MCP_BROWSER_USE_OWN_BROWSER=false)
MCP_BROWSER_KEEP_OPEN=false
# Optional: Directory to save Playwright trace files (useful for debugging). If not set, tracing to file is disabled.
# MCP_BROWSER_TRACE_PATH=./tmp/trace

# === Agent Tool Configuration (`run_browser_agent` tool, MCP_AGENT_TOOL_*) ===
MCP_AGENT_TOOL_MAX_STEPS=100
MCP_AGENT_TOOL_MAX_ACTIONS_PER_STEP=5
# Method for tool invocation ('auto', 'json_schema', 'function_calling')
MCP_AGENT_TOOL_TOOL_CALLING_METHOD=auto
MCP_AGENT_TOOL_MAX_INPUT_TOKENS=128000
# Enable vision capabilities (screenshot analysis)
MCP_AGENT_TOOL_USE_VISION=true
# Override general browser headless mode for this tool (true/false/empty for general setting)
# MCP_AGENT_TOOL_HEADLESS=
# Override general browser disable security for this tool (true/false/empty for general setting)
# MCP_AGENT_TOOL_DISABLE_SECURITY=
# Enable Playwright video recording (true/false)
MCP_AGENT_TOOL_ENABLE_RECORDING=false
# Optional: Path to save agent run video recordings. If not set, recording to file is disabled even if ENABLE_RECORDING=true.
# MCP_AGENT_TOOL_SAVE_RECORDING_PATH=./tmp/recordings
# Optional: Directory to save agent history JSON files. If not set, history saving is disabled.
# MCP_AGENT_TOOL_HISTORY_PATH=./tmp/agent_history

# === Deep Research Tool Configuration (`run_deep_research` tool, MCP_RESEARCH_TOOL_*) ===
MCP_RESEARCH_TOOL_MAX_PARALLEL_BROWSERS=3
# MANDATORY: Base directory to save research artifacts (report, results). Task ID will be appended.
# Example: MCP_RESEARCH_TOOL_SAVE_DIR=/mnt/data/research_outputs
# Example: MCP_RESEARCH_TOOL_SAVE_DIR=C:\\Users\\<USER>\\Documents\\ResearchData
MCP_RESEARCH_TOOL_SAVE_DIR=./tmp/deep_research

# === Path Configuration (MCP_PATHS_*) ===
# Optional: Directory for downloaded files. If not set, persistent downloads to a specific path are disabled.
# MCP_PATHS_DOWNLOADS=./tmp/downloads

# === Server Configuration (MCP_SERVER_*) ===
# Path for the server log file. Leave empty for stdout.
# MCP_SERVER_LOG_FILE=mcp_server_browser_use.log
# Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL
MCP_SERVER_LOGGING_LEVEL=INFO
# Enable/disable anonymized telemetry (true/false)
MCP_SERVER_ANONYMIZED_TELEMETRY=true
# Optional: JSON string for MCP client configuration for the controller
# MCP_SERVER_MCP_CONFIG='{"client_name": "mcp-browser-use-controller"}'
