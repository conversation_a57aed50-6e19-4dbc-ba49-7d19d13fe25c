import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function DELETE(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { propertyId, storagePath } = await request.json();

    if (!propertyId || !storagePath) {
      return NextResponse.json(
        { error: 'Property ID and storage path are required' },
        { status: 400 }
      );
    }

    // Verify user is authenticated
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify user owns the property
    const { data: property } = await supabase
      .from('properties')
      .select('seller_id, images')
      .eq('id', propertyId)
      .single();

    if (!property || property.seller_id !== user.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Delete from Supabase Storage
    const { error: deleteError } = await supabase.storage
      .from('property-images')
      .remove([storagePath]);

    if (deleteError) {
      console.error('Delete error:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete image' },
        { status: 500 }
      );
    }

    // Update property images in database
    const updatedImages = property.images.filter(
      (img: { storagePath: string }) => img.storagePath !== storagePath
    );

    const { error: updateError } = await supabase
      .from('properties')
      .update({
        images: updatedImages,
      })
      .eq('id', propertyId);

    if (updateError) {
      console.error('Update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to update property' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting image:', error);
    return NextResponse.json(
      { error: 'Failed to delete image' },
      { status: 500 }
    );
  }
} 