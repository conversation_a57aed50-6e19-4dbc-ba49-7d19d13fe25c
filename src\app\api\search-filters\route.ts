import { auth } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';
import prisma from '@/libs/prisma';

export async function POST(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { filterName, filterData } = body;

    if (!filterName) {
      return NextResponse.json({ message: 'Filter name is required' }, { status: 400 });
    }

    if (!filterData) {
      return NextResponse.json({ message: 'Filter data is required' }, { status: 400 });
    }

    // Find the user in our database
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return NextResponse.json({ message: 'User not found' }, { status: 404 });
    }

    // Create the search filter
    const searchFilter = await prisma.searchFilter.create({
      data: {
        userId: user.id,
        filterName,
        filterData
      }
    });

    return NextResponse.json({
      message: 'Search filter saved successfully',
      searchFilter
    }, { status: 201 });

  } catch (error) {
    console.error('Error saving search filter:', error);
    return NextResponse.json(
      { message: 'Error saving search filter', error: error.message },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Get all search filters for the current user
    const searchFilters = await prisma.searchFilter.findMany({
      where: {
        userId: userId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json(searchFilters);

  } catch (error) {
    console.error('Error fetching search filters:', error);
    return NextResponse.json(
      { message: 'Error fetching search filters', error: error.message },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const filterId = searchParams.get('id');

    if (!filterId) {
      return NextResponse.json({ message: 'Filter ID required' }, { status: 400 });
    }

    // Delete the search filter (only if it belongs to the current user)
    const deletedFilter = await prisma.searchFilter.deleteMany({
      where: {
        id: filterId,
        userId: userId
      }
    });

    if (deletedFilter.count === 0) {
      return NextResponse.json({ message: 'Filter not found or unauthorized' }, { status: 404 });
    }

    return NextResponse.json({
      message: 'Search filter deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting search filter:', error);
    return NextResponse.json(
      { message: 'Error deleting search filter', error: error.message },
      { status: 500 }
    );
  }
}
