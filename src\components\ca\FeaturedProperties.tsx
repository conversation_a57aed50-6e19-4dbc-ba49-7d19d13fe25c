'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Heart, MapPin, Bed, Bath, Square, Eye, Calendar } from 'lucide-react'
import Image from 'next/image'

interface FeaturedPropertiesProps {
  userType: 'buyer' | 'seller'
}

// Mock property data
const mockProperties = [
  {
    id: 1,
    title: "Modern Downtown Condo",
    price: 650000,
    location: "Toronto, ON",
    bedrooms: 2,
    bathrooms: 2,
    sqft: 1200,
    image: "/api/placeholder/400/300",
    featured: true,
    views: 245,
    daysListed: 5
  },
  {
    id: 2,
    title: "Family Home with Garden",
    price: 850000,
    location: "Vancouver, BC",
    bedrooms: 4,
    bathrooms: 3,
    sqft: 2400,
    image: "/api/placeholder/400/300",
    featured: true,
    views: 189,
    daysListed: 12
  },
  {
    id: 3,
    title: "Luxury Townhouse",
    price: 750000,
    location: "Calgary, AB",
    bedrooms: 3,
    bathrooms: 2.5,
    sqft: 1800,
    image: "/api/placeholder/400/300",
    featured: true,
    views: 156,
    daysListed: 8
  }
]

export function FeaturedProperties({ userType }: FeaturedPropertiesProps) {
  // Replace mockProperties with real data if available in the future
  const properties = mockProperties.map((property) => ({
    ...property,
    image: property.image || '/default-property.jpg',
  }))

  return (
    <section className="py-16 bg-background w-full">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          {userType === 'seller' ? 'Success Stories' : 'Featured Properties'}
        </h2>
        <p className="text-lg text-gray-600">
          {userType === 'seller'
            ? 'See how sellers are saving thousands with SoNoBrokers'
            : 'Discover commission-free properties from verified sellers'}
        </p>
      </div>
      <div className="w-full px-4">
        <div className="grid md:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {properties.map((property) => (
            <div key={property.id} className="bg-background rounded-2xl shadow-lg border flex flex-col h-full">
              <div className="relative h-48 bg-gray-100 rounded-t-2xl overflow-hidden">
                <img src={property.image} alt={property.title} className="object-cover w-full h-full" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10" />
                <Badge className="absolute top-3 left-3 z-20 bg-orange-500 text-white">Featured</Badge>
                <Button variant="ghost" size="sm" className="absolute top-3 right-3 z-20 text-white hover:text-red-500 hover:bg-white/20"><Heart className="h-4 w-4" /></Button>
                <div className="absolute bottom-3 left-3 z-20 text-white">
                  <div className="flex items-center gap-4 text-xs">
                    <div className="flex items-center gap-1"><Eye className="h-3 w-3" /><span>{property.views}</span></div>
                    <div className="flex items-center gap-1"><Calendar className="h-3 w-3" /><span>{property.daysListed}d ago</span></div>
                  </div>
                </div>
              </div>
              <div className="p-6 flex-1 flex flex-col justify-between">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-orange-500 transition-colors">{property.title}</h3>
                  <div className="flex items-center text-gray-600 mb-2"><MapPin className="h-4 w-4 mr-1" /><span className="text-sm">{property.location}</span></div>
                  <p className="text-2xl font-bold text-gray-900">${property.price.toLocaleString()}</p>
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-600 mt-4">
                  <div className="flex items-center gap-1"><Bed className="h-4 w-4" /><span>{property.bedrooms} bed</span></div>
                  <div className="flex items-center gap-1"><Bath className="h-4 w-4" /><span>{property.bathrooms} bath</span></div>
                  <div className="flex items-center gap-1"><Square className="h-4 w-4" /><span>{property.sqft} sqft</span></div>
                </div>
                <div className="pt-2"><Badge variant="outline" className="text-xs">No Commission</Badge></div>
              </div>
            </div>
          ))}
        </div>
        <div className="text-center mt-12">
          <Button size="lg" className="text-lg px-8 bg-orange-500 hover:bg-orange-600 text-white rounded-full">View All Properties</Button>
        </div>
      </div>
    </section>
  )
}
