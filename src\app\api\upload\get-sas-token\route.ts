import { BlobServiceClient, StorageSharedKeyCredential, BlobSASPermissions } from '@azure/storage-blob';
import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME;
const accountKey = process.env.AZURE_STORAGE_ACCOUNT_KEY;
const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME;

// Check if Azure Storage is configured
const isAzureConfigured = accountName && accountKey && containerName;

if (!isAzureConfigured) {
  console.warn('Azure Storage credentials are not configured. Upload functionality will be disabled.');
}

// Only initialize Azure clients if configured
let sharedKeyCredential: StorageSharedKeyCredential | null = null;
let blobServiceClient: BlobServiceClient | null = null;

if (isAzureConfigured) {
  sharedKeyCredential = new StorageSharedKeyCredential(accountName!, accountKey!);
  blobServiceClient = new BlobServiceClient(
    `https://${accountName}.blob.core.windows.net`,
    sharedKeyCredential
  );
}

export async function POST(request: Request) {
  try {
    // Check if Azure Storage is configured
    if (!isAzureConfigured) {
      return new NextResponse('Azure Storage is not configured', { status: 503 });
    }

    const { userId } = auth();
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const { fileName, contentType } = await request.json();

    if (!fileName || !contentType) {
      return new NextResponse('Missing required fields', { status: 400 });
    }

    const containerClient = blobServiceClient!.getContainerClient(containerName!);
    const blobClient = containerClient.getBlobClient(fileName);

    // Generate SAS token with 1 hour expiry
    const sasToken = await blobClient.generateSasUrl({
      permissions: BlobSASPermissions.parse("cw"), // create and write permissions
      expiresOn: new Date(new Date().valueOf() + 3600 * 1000), // 1 hour
      contentType,
    });

    return NextResponse.json({ sasUrl: sasToken });
  } catch (error) {
    console.error('Error generating SAS token:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}