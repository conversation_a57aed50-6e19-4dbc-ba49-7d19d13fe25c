'use client'

import { Card, CardContent } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils"

interface Location {
  location: string
  averagePrice: number
  totalListings: number
  priceChange: number
}

interface PropertyType {
  type: string
  count: number
  averagePrice: number
  priceChange: number
}

interface MarketTrendsProps {
  trends: {
    averagePrice: number
    priceChange: number
    totalListings: number
    listingsChange: number
    averageDaysOnMarket: number
    daysOnMarketChange: number
    topLocations: Location[]
    propertyTypes: PropertyType[]
  }
}

export function MarketTrends({ trends }: MarketTrendsProps) {
  const {
    averagePrice,
    priceChange,
    totalListings,
    listingsChange,
    averageDaysOnMarket,
    daysOnMarketChange,
    topLocations,
    propertyTypes
  } = trends

  const getTrendIcon = (change: number) => {
    if (change > 0) return '↗️'
    if (change < 0) return '↘️'
    return '→'
  }

  const getTrendClass = (change: number, inverse = false) => {
    if (change === 0) return 'text-muted-foreground'
    if (inverse) {
      return change > 0 ? 'text-red-500' : 'text-green-500'
    }
    return change > 0 ? 'text-green-500' : 'text-red-500'
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{formatCurrency(averagePrice)}</div>
            <div className="flex items-center">
              <p className="text-sm text-muted-foreground mr-1">Average Price</p>
              <span className={getTrendClass(priceChange)}>
                {getTrendIcon(priceChange)} {Math.abs(priceChange)}%
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{totalListings}</div>
            <div className="flex items-center">
              <p className="text-sm text-muted-foreground mr-1">Total Listings</p>
              <span className={getTrendClass(listingsChange)}>
                {getTrendIcon(listingsChange)} {Math.abs(listingsChange)}%
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{averageDaysOnMarket}</div>
            <div className="flex items-center">
              <p className="text-sm text-muted-foreground mr-1">Days on Market</p>
              <span className={getTrendClass(daysOnMarketChange, true)}>
                {getTrendIcon(daysOnMarketChange)} {Math.abs(daysOnMarketChange)}%
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-medium mb-2">Top Locations</h3>
          <div className="space-y-2">
            {topLocations.map((location, index) => (
              <div key={index} className="flex justify-between items-center p-3 border rounded-md">
                <div>
                  <p className="font-medium">{location.location}</p>
                  <p className="text-sm text-muted-foreground">{location.totalListings} listings</p>
                </div>
                <div>
                  <p className="font-medium text-right">{formatCurrency(location.averagePrice)}</p>
                  <p className={`text-sm text-right ${getTrendClass(location.priceChange)}`}>
                    {getTrendIcon(location.priceChange)} {Math.abs(location.priceChange)}%
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-medium mb-2">Property Types</h3>
          <div className="space-y-2">
            {propertyTypes.map((type, index) => (
              <div key={index} className="flex justify-between items-center p-3 border rounded-md">
                <div>
                  <p className="font-medium">{type.type}</p>
                  <p className="text-sm text-muted-foreground">{type.count} properties</p>
                </div>
                <div>
                  <p className="font-medium text-right">{formatCurrency(type.averagePrice)}</p>
                  <p className={`text-sm text-right ${getTrendClass(type.priceChange)}`}>
                    {getTrendIcon(type.priceChange)} {Math.abs(type.priceChange)}%
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
} 
