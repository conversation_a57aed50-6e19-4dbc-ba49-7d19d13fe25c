import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TermsOfService() {
  return (
    <div className="container mx-auto py-12 px-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-3xl font-bold">Terms of Service</CardTitle>
          <p className="text-muted-foreground">Last updated: {new Date().toLocaleDateString()}</p>
        </CardHeader>
        <CardContent className="space-y-8">
          <section>
            <h2 className="text-2xl font-semibold mb-4">1. Agreement to Terms</h2>
            <p className="text-muted-foreground">
              By accessing or using SoNoBrokers, you agree to be bound by these Terms of Service and all applicable laws and regulations. If you do not agree with any of these terms, you are prohibited from using or accessing this platform.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">2. User Accounts</h2>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                To use certain features of our platform, you must register for an account. You agree to:
              </p>
              <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
                <li>Provide accurate and complete information</li>
                <li>Maintain the security of your account</li>
                <li>Promptly update any changes to your information</li>
                <li>Accept responsibility for all activities under your account</li>
              </ul>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">3. User Roles and Responsibilities</h2>
            <div className="space-y-4">
              <h3 className="text-xl font-medium">3.1 Property Sellers</h3>
              <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
                <li>Provide accurate property information</li>
                <li>Maintain current and valid property listings</li>
                <li>Respond to inquiries in a timely manner</li>
                <li>Comply with all applicable real estate laws</li>
              </ul>

              <h3 className="text-xl font-medium">3.2 Property Buyers</h3>
              <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
                <li>Provide accurate personal information</li>
                <li>Use the platform in good faith</li>
                <li>Respect seller privacy and property rights</li>
                <li>Comply with all applicable laws</li>
              </ul>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">4. Payment Terms</h2>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                We use Stripe for payment processing. By using our payment services, you agree to:
              </p>
              <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
                <li>Provide valid payment information</li>
                <li>Pay all fees and charges on time</li>
                <li>Comply with Stripe's terms of service</li>
                <li>Accept our refund and cancellation policies</li>
              </ul>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">5. Authentication and Security</h2>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                We use Clerk for authentication and user management. You agree to:
              </p>
              <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
                <li>Keep your authentication credentials secure</li>
                <li>Not share your account access</li>
                <li>Report any security breaches immediately</li>
                <li>Comply with Clerk's terms of service</li>
              </ul>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">6. Prohibited Activities</h2>
            <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
              <li>Posting false or misleading information</li>
              <li>Harassing or abusing other users</li>
              <li>Violating any applicable laws or regulations</li>
              <li>Attempting to bypass security measures</li>
              <li>Using the platform for illegal purposes</li>
            </ul>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">7. Intellectual Property</h2>
            <p className="text-muted-foreground">
              All content on SoNoBrokers, including text, graphics, logos, and software, is the property of SoNoBrokers or its content suppliers and is protected by international copyright laws.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">8. Limitation of Liability</h2>
            <p className="text-muted-foreground">
              SoNoBrokers shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of or inability to use the platform.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">9. Changes to Terms</h2>
            <p className="text-muted-foreground">
              We reserve the right to modify these terms at any time. We will notify users of any material changes via email or through the platform.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">10. Contact Information</h2>
            <p className="text-muted-foreground">
              For questions about these Terms of Service, please contact us at:
            </p>
            <p className="text-muted-foreground">
              Email: <EMAIL>
            </p>
          </section>
        </CardContent>
      </Card>
    </div>
  );
}