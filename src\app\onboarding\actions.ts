"use server";
import { currentUser } from '@clerk/nextjs/server';
import { prisma } from '@/libs/prisma';

export async function updateUserRole(role: 'seller' | 'buyer') {
  const user = await currentUser();
  if (!user) throw new Error('Not authenticated');

  // Update userType in the User table
  await prisma.user.update({
    where: { email: user.emailAddresses[0].emailAddress },
    data: { userType: role },
  });

  // Create profile if not exists
  if (role === 'seller') {
    await prisma.sellerProfile.upsert({
      where: { userId: user.id },
      update: {},
      create: { userId: user.id },
    });
  } else if (role === 'buyer') {
    await prisma.buyerProfile.upsert({
      where: { userId: user.id },
      update: {},
      create: { userId: user.id },
    });
  }

  // Optionally, update Clerk public metadata
  // await user.update({ publicMetadata: { role } });

  return { success: true };
} 