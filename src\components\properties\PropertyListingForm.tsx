'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Save,
  Sparkles,
  Home,
  MapPin,
  DollarSign,
  Bed,
  Bath,
  Car,
  Calendar,
  Ruler,
  Camera,
  FileText,
  Eye,
  ArrowLeft,
  Building,
  Settings,
  Image,
  ShoppingCart,
  File
} from 'lucide-react';
import { SelectorChips } from '@/components/ui/selector-chips';

// Comprehensive Property Features
const PROPERTY_FEATURES = [
  'Brick Finishes', 'Walk-in Closet', 'Hardwood Floors', 'Recessed Lighting', 'Dining Room',
  'Fireplace', 'Quartz Countertops', 'Landscaped', 'Parking', 'Stainless Steel',
  'Curb Appeal', 'Laundry', 'Ceiling Fan', 'Custom Cabinetry', 'Garage',
  'Double Sink', 'Spa', 'Outdoor Lighting', 'Quiet Area', 'Beachfront',
  'Ocean View', 'Lake View', 'Mountain View', 'Park View', 'Fenced',
  'Patio', 'Outdoor Fire', 'Hot Tub', 'Pool', 'Irrigation System',
  'Fruit Trees', 'Garden', 'Basketball Court', 'Tennis Court', 'Solar Panels',
  'RV or Boat Parking', 'Dock', 'Rooftop Deck', 'Asphalt Roof', 'Water Feature',
  'Pergola', 'Stone Finishes', 'Stucco Finishes', 'Wood Siding', 'Vinyl Siding',
  'Concrete Features', 'Glass Features', 'Facade', 'Log Siding', 'Outdoor Storage',
  'Corner Lot', 'Roof Condition', 'Walkways', 'Chimney', 'Outdoor Kitchen',
  'Outdoor Dining Area', 'Playground Equipment', 'Vegetable Garden', 'Side Yard',
  'Garden Beds', 'Flower Beds', 'BBQ Area', 'Outdoor Pizza Oven', 'Outdoor Shower',
  'Carport', 'Architectural Style', 'Painted Exterior', 'Driveway Gate',
  'Apartment Stories', 'Stamped Concrete', 'Outdoor Ceiling Fans', 'Gravel Driveway',
  'Outdoor Speakers', 'Vineyard', 'Shopping & Dining', 'Nearby Schools',
  'Neighbourhood Amenities', 'Skylights', 'Open Floor Plan', 'Vinyl Floors',
  'Vaulted Ceilings', 'Smart Home', 'Central Vac', 'High Ceilings',
  'Air Conditioning', 'Heating System', 'Crown Molding', 'Jacuzzi Tub',
  'Wine Cellar', 'Home Office', 'Exercise Room', 'Media Room',
  'Home Theatre', 'Sauna', 'Wet Bar', 'Sunroom', 'Rec Room',
  'Den', 'Yoga Studio', 'Library', 'Ensuite Bathroom', 'Storage',
  'Granite Countertops', 'Marble Countertops', 'Pantry', 'Kitchen Island',
  'Renovated', 'Gas Range', 'Wine Fridge', 'Soft Close Drawers',
  'Carpet Flooring', 'Tile Flooring', 'Heated Floors', 'Laminate Flooring',
  'Ice Maker', 'Dishwasher', 'Smart Appliances', 'Elevator', 'In-law Suite',
  'French Doors', 'Built-in Shelving', 'Mudroom', 'Loft Area',
  'Picture Windows', 'Breakfast Nook', 'Convection Oven', 'Chefs Kitchen',
  'Built-in Microwave', 'Double Oven', 'Walk-in Pantry', 'Water Filtration System',
  'Guest Bedrooms', 'Paint Details', 'Window Coverings', 'Staircase',
  'Interior Columns', 'Brick or Stone Accents', 'Architectural Details',
  'Energy Efficiency', 'Track Lighting', 'Bi-Fold Doors', 'Hot Water Tank',
  'Pocket Doors', 'Chandeliers', 'Transom Windows', 'Pendant Lighting',
  'Tray Ceilings', 'Exposed Brick Walls', 'Dimmer Switches', 'Home Automation System',
  'Security System', 'Glass Doors', 'Backsplash', 'Pot Filler',
  'Induction Cooktop', 'Farmhouse Sink', 'Glass Front Cabinets', 'Under-Cabinet Lighting',
  'Wine Rack', 'Kitchen Desk', 'Pantry Shelving', 'Pot Rack',
  'Range Hood', 'Professional-Grade Appliances', 'Double Islands', 'Scullery',
  'Integrated Appliances', 'Guest Bathroom', 'His and Hers Closets', 'Mirrored Closet Doors',
  'Walk-In Shower', 'Vanities', 'Billiard Room', 'Murphy Bed',
  'Attic', 'Grand Foyer', 'Built-In Oven', 'Legal Suite'
];

// Security Features Options
const SECURITY_FEATURES = [
  'Security System', 'Gated Community', 'Security Cameras', 'Motion Sensors',
  'Alarm System', 'Intercom System', 'Keypad Entry', 'Smart Locks',
  'Security Lighting', 'Fence', 'Guard House', 'Access Control'
];

// Appliances Included Options
const APPLIANCES_INCLUDED = [
  'Refrigerator', 'Dishwasher', 'Stove/Oven', 'Microwave', 'Washer',
  'Dryer', 'Wine Fridge', 'Ice Maker', 'Garbage Disposal', 'Range Hood',
  'Built-in Vacuum', 'Water Heater', 'Air Conditioning', 'Heating System'
];

// Sidebar Navigation Items
const SIDEBAR_SECTIONS = [
  {
    id: 'information',
    label: 'Property Information',
    icon: Building,
    color: 'bg-blue-500'
  },
  {
    id: 'specs',
    label: 'Property Specs',
    icon: Settings,
    color: 'bg-purple-500'
  },
  {
    id: 'media',
    label: 'Property Media',
    icon: Image,
    color: 'bg-green-500'
  },
  {
    id: 'marketplace',
    label: 'Ads Marketplace',
    icon: ShoppingCart,
    color: 'bg-orange-500'
  },
  {
    id: 'documents',
    label: 'Property Documents',
    icon: File,
    color: 'bg-red-500'
  }
];

interface PropertyData {
  // Property Information
  title: string;
  description: string;
  address: string;
  city: string;
  province: string;
  postalCode: string;
  propertyType: string;
  adType: string;

  // Property Specs
  yearBuilt: string;
  landSize: string;
  interiorSize: string;
  askingPrice: string;
  listing: string;
  leasePrice: string;
  bedrooms: string;
  bathrooms: string;
  halfBathrooms: string;
  currency: string;
  leaseTerms: string;
  parkingSpaces: string;
  heating: string;
  cooling: string;
  basement: string;
  roof: string;
  hoa: string;
  tax: string;
  schoolDistrict: string;
  walkScore: string;
  transitScore: string;
  bikeScore: string;
  petPolicy: string;
  viewType: string;
  energyEfficiencyRating: string;
  dimensions: string;
  waterfront: string;
  securityFeatures: string[];
  appliancesIncluded: string[];
  ceilingHeight: string;
  foundationType: string;
  sanitarySystem: string;
  internetAvailable: string;
  sunlightExposure: string;
  communityEvents: string;
  internetSpeeds: string;
  energyCosts: string;
  waterNearby: string;
  airportsNearby: string;
  emergencyServicesNearby: string;
  educationalServicesNearby: string;
  portfolio: string;
  propertyShortSummary: string;
  propertyHeadline: string;

  // Property Features
  propertyFeatures: string[];

  // Property Media
  virtualTour: string;
  photos: string[];

  // Ads Marketplace
  mlsNumber: string;
  listingAgent: string;
  contactInfo: string;

  // Property Documents
  documents: string[];
}

export { PropertyListingFormNew as PropertyListingForm } from './PropertyListingFormNew';

export function PropertyListingFormOld() {
  const { user, isSignedIn } = useUser();
  const router = useRouter();
  const [activeSection, setActiveSection] = useState('information');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [showAIImport, setShowAIImport] = useState(false);
  const [aiAddress, setAiAddress] = useState('');
  const [isImporting, setIsImporting] = useState(false);

  const [propertyData, setPropertyData] = useState<PropertyData>({
    // Property Information
    title: '',
    description: '',
    address: '',
    city: '',
    province: '',
    postalCode: '',
    propertyType: '',
    adType: '',

    // Property Specs
    yearBuilt: '',
    landSize: '',
    interiorSize: '',
    askingPrice: '',
    listing: '',
    leasePrice: '',
    bedrooms: '',
    bathrooms: '',
    halfBathrooms: '',
    currency: 'CAD',
    leaseTerms: '',
    parkingSpaces: '',
    heating: '',
    cooling: '',
    basement: '',
    roof: '',
    hoa: '',
    tax: '',
    schoolDistrict: '',
    walkScore: '',
    transitScore: '',
    bikeScore: '',
    petPolicy: '',
    viewType: '',
    energyEfficiencyRating: '',
    dimensions: '',
    waterfront: '',
    securityFeatures: [],
    appliancesIncluded: [],
    ceilingHeight: '',
    foundationType: '',
    sanitarySystem: '',
    internetAvailable: '',
    sunlightExposure: '',
    communityEvents: '',
    internetSpeeds: '',
    energyCosts: '',
    waterNearby: '',
    airportsNearby: '',
    emergencyServicesNearby: '',
    educationalServicesNearby: '',
    portfolio: '',
    propertyShortSummary: '',
    propertyHeadline: '',

    // Property Features
    propertyFeatures: [],

    // Property Media
    virtualTour: '',
    photos: [],

    // Ads Marketplace
    mlsNumber: '',
    listingAgent: '',
    contactInfo: '',

    // Property Documents
    documents: [],
  });

  // Auto-save functionality
  useEffect(() => {
    const autoSave = setTimeout(() => {
      if (isSignedIn && Object.values(propertyData).some(value =>
        Array.isArray(value) ? value.length > 0 : value !== ''
      )) {
        handleSaveDraft();
      }
    }, 30000); // Auto-save every 30 seconds

    return () => clearTimeout(autoSave);
  }, [propertyData, isSignedIn]);

  // Load saved draft on component mount
  useEffect(() => {
    if (isSignedIn) {
      loadSavedDraft();
    }
  }, [isSignedIn]);

  const loadSavedDraft = async () => {
    try {
      const saved = localStorage.getItem(`property-draft-${user?.id}`);
      if (saved) {
        const draftData = JSON.parse(saved);
        setPropertyData(draftData.data);
        setLastSaved(new Date(draftData.timestamp));
      }
    } catch (error) {
      console.error('Error loading draft:', error);
    }
  };

  const handleSaveDraft = async () => {
    if (!isSignedIn) return;

    setIsSaving(true);
    try {
      const draftData = {
        data: propertyData,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem(`property-draft-${user?.id}`, JSON.stringify(draftData));
      setLastSaved(new Date());
    } catch (error) {
      console.error('Error saving draft:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleAIImport = async () => {
    if (!aiAddress.trim()) return;

    setIsImporting(true);
    try {
      // Simulate AI import - replace with actual API call
      const response = await fetch('/api/ai-property-import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ address: aiAddress })
      });

      if (response.ok) {
        const aiData = await response.json();
        setPropertyData(prev => ({
          ...prev,
          ...aiData
        }));
        setShowAIImport(false);
        setAiAddress('');
      }
    } catch (error) {
      console.error('Error importing AI data:', error);
    } finally {
      setIsImporting(false);
    }
  };

  const handleSubmit = async () => {
    if (!isSignedIn) {
      router.push('/sign-in?redirect=/list-property');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/property-listings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(propertyData)
      });

      if (response.ok) {
        // Clear draft after successful submission
        localStorage.removeItem(`property-draft-${user?.id}`);
        router.push('/dashboard/properties?success=listing-created');
      } else {
        throw new Error('Failed to submit listing');
      }
    } catch (error) {
      console.error('Error submitting listing:', error);
      alert('Error submitting listing. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const updatePropertyData = (field: keyof PropertyData, value: any) => {
    setPropertyData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Helper functions for progress tracking
  const getCompletedFieldsCount = (sectionId: string): number => {
    switch (sectionId) {
      case 'information':
        return [
          propertyData.title, propertyData.description, propertyData.address,
          propertyData.city, propertyData.province, propertyData.postalCode,
          propertyData.propertyType, propertyData.adType
        ].filter(field => field && field.trim() !== '').length;

      case 'specs':
        return [
          propertyData.yearBuilt, propertyData.landSize, propertyData.interiorSize,
          propertyData.askingPrice, propertyData.bedrooms, propertyData.bathrooms,
          propertyData.halfBathrooms, propertyData.parkingSpaces, propertyData.heating,
          propertyData.cooling, propertyData.basement, propertyData.roof
        ].filter(field => field && field.trim() !== '').length +
          (propertyData.securityFeatures.length > 0 ? 1 : 0) +
          (propertyData.appliancesIncluded.length > 0 ? 1 : 0) +
          (propertyData.propertyFeatures.length > 0 ? 1 : 0);

      case 'media':
        return [propertyData.virtualTour].filter(field => field && field.trim() !== '').length +
          (propertyData.photos.length > 0 ? 1 : 0);

      case 'marketplace':
        return [propertyData.mlsNumber, propertyData.listingAgent, propertyData.contactInfo]
          .filter(field => field && field.trim() !== '').length;

      case 'documents':
        return propertyData.documents.length > 0 ? 1 : 0;

      default:
        return 0;
    }
  };

  const getTotalFieldsCount = (sectionId: string): number => {
    switch (sectionId) {
      case 'information': return 8;
      case 'specs': return 15;
      case 'media': return 2;
      case 'marketplace': return 3;
      case 'documents': return 1;
      default: return 0;
    }
  };

  return (
    <div className="w-full min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-40">
        <div className="w-full px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="h-8 px-2"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back
              </Button>
              <div className="flex items-center gap-2">
                <Home className="h-5 w-5 text-primary" />
                <h1 className="text-xl font-semibold">List Your Property</h1>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {lastSaved && (
                <span className="text-xs text-muted-foreground">
                  Last saved: {lastSaved.toLocaleTimeString()}
                </span>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={handleSaveDraft}
                disabled={isSaving || !isSignedIn}
                className="h-8 px-2"
              >
                <Save className="h-4 w-4 mr-1" />
                {isSaving ? 'Saving...' : 'Save Draft'}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAIImport(!showAIImport)}
                className="h-8 px-2 bg-gradient-to-r from-orange-50 to-green-50 border-orange-200"
              >
                <Sparkles className="h-4 w-4 mr-1 text-orange-500" />
                AI Import
              </Button>
            </div>
          </div>

          {/* AI Import Section */}
          {showAIImport && (
            <div className="mt-3 p-3 border border-orange-200 rounded-lg bg-gradient-to-r from-orange-50/50 to-green-50/50">
              <div className="flex items-center gap-2 mb-2">
                <Sparkles className="h-4 w-4 text-orange-500" />
                <span className="text-sm font-medium">Import Property Data with AI</span>
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="Enter property address (e.g., 123 Main St, Toronto, ON)"
                  value={aiAddress}
                  onChange={(e) => setAiAddress(e.target.value)}
                  className="flex-1 h-8"
                />
                <Button
                  size="sm"
                  onClick={handleAIImport}
                  disabled={isImporting || !aiAddress.trim()}
                  className="h-8 px-3"
                >
                  {isImporting ? 'Importing...' : 'Import'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full flex">
        {/* Left Sidebar Navigation */}
        <div className="w-80 border-r border-border bg-card/50 p-4">
          <ScrollArea className="h-[calc(100vh-120px)]">
            <div className="space-y-2">
              {SIDEBAR_SECTIONS.map((section) => {
                const Icon = section.icon;
                const isActive = activeSection === section.id;
                const completedFields = getCompletedFieldsCount(section.id);
                const totalFields = getTotalFieldsCount(section.id);
                const progress = totalFields > 0 ? (completedFields / totalFields) * 100 : 0;

                return (
                  <div key={section.id} className="space-y-1">
                    <button
                      onClick={() => setActiveSection(section.id)}
                      className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors ${isActive
                        ? 'bg-primary/10 border border-primary/20 text-primary'
                        : 'hover:bg-muted/50 text-muted-foreground hover:text-foreground'
                        }`}
                    >
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${section.color} text-white`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm">{section.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {completedFields}/{totalFields} completed
                        </div>
                      </div>
                      <div className="text-xs font-medium">
                        {Math.round(progress)}%
                      </div>
                    </button>

                    {/* Progress Bar */}
                    <div className="ml-11 mr-3">
                      <div className="w-full bg-muted rounded-full h-1">
                        <div
                          className={`h-1 rounded-full transition-all duration-300 ${section.color}`}
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Save Section Button */}
            <div className="mt-6 pt-4 border-t border-border">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSaveDraft()}
                disabled={isSaving || !isSignedIn}
                className="w-full h-8"
              >
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? 'Saving Section...' : 'Save Current Section'}
              </Button>
            </div>
          </ScrollArea>
        </div>

        {/* Right Panel - Form Content */}
        <div className="flex-1 p-6">
          <ScrollArea className="h-[calc(100vh-120px)]">
            <div className="space-y-4">

              {/* Property Information Section */}
              {activeSection === 'information' && (
                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <MapPin className="h-5 w-5" />
                        Property Location
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="md:col-span-2">
                          <Label htmlFor="address">Street Address</Label>
                          <Input
                            id="address"
                            value={propertyData.address}
                            onChange={(e) => updatePropertyData('address', e.target.value)}
                            placeholder="123 Main Street"
                            className="h-9"
                          />
                        </div>
                        <div>
                          <Label htmlFor="city">City</Label>
                          <Input
                            id="city"
                            value={propertyData.city}
                            onChange={(e) => updatePropertyData('city', e.target.value)}
                            placeholder="Toronto"
                            className="h-9"
                          />
                        </div>
                        <div>
                          <Label htmlFor="province">Province</Label>
                          <Select value={propertyData.province} onValueChange={(value) => updatePropertyData('province', value)}>
                            <SelectTrigger className="h-9">
                              <SelectValue placeholder="Select province" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="ON">Ontario</SelectItem>
                              <SelectItem value="BC">British Columbia</SelectItem>
                              <SelectItem value="AB">Alberta</SelectItem>
                              <SelectItem value="QC">Quebec</SelectItem>
                              <SelectItem value="NS">Nova Scotia</SelectItem>
                              <SelectItem value="NB">New Brunswick</SelectItem>
                              <SelectItem value="MB">Manitoba</SelectItem>
                              <SelectItem value="SK">Saskatchewan</SelectItem>
                              <SelectItem value="PE">Prince Edward Island</SelectItem>
                              <SelectItem value="NL">Newfoundland and Labrador</SelectItem>
                              <SelectItem value="YT">Yukon</SelectItem>
                              <SelectItem value="NT">Northwest Territories</SelectItem>
                              <SelectItem value="NU">Nunavut</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="postalCode">Postal Code</Label>
                          <Input
                            id="postalCode"
                            value={propertyData.postalCode}
                            onChange={(e) => updatePropertyData('postalCode', e.target.value)}
                            placeholder="M5V 3A8"
                            className="h-9"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Home className="h-5 w-5" />
                        Property Details
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="title">Property Title</Label>
                        <Input
                          id="title"
                          value={propertyData.title}
                          onChange={(e) => updatePropertyData('title', e.target.value)}
                          placeholder="Beautiful Family Home in Hamilton"
                          className="h-9"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label>Property Type</Label>
                          <SelectorChips
                            options={['House', 'Condo', 'Townhouse', 'Land']}
                            onChange={(selected) => updatePropertyData('propertyType', selected[0] || '')}
                            maxSelections={1}
                          />
                        </div>
                        <div>
                          <Label>Ad Type</Label>
                          <SelectorChips
                            options={['Sale', 'Rent', 'Lease']}
                            onChange={(selected) => updatePropertyData('adType', selected[0] || '')}
                            maxSelections={1}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label htmlFor="price">Price</Label>
                          <div className="relative">
                            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                              id="price"
                              value={propertyData.price}
                              onChange={(e) => updatePropertyData('price', e.target.value)}
                              placeholder="750,000"
                              className="h-9 pl-9"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="size">Size (sq ft)</Label>
                          <div className="relative">
                            <Ruler className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                              id="size"
                              value={propertyData.size}
                              onChange={(e) => updatePropertyData('size', e.target.value)}
                              placeholder="2100"
                              className="h-9 pl-9"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="yearBuilt">Year Built</Label>
                          <div className="relative">
                            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                              id="yearBuilt"
                              value={propertyData.yearBuilt}
                              onChange={(e) => updatePropertyData('yearBuilt', e.target.value)}
                              placeholder="2010"
                              className="h-9 pl-9"
                            />
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label>Bedrooms</Label>
                          <SelectorChips
                            options={['1', '2', '3', '4', '5', '6+']}
                            onChange={(selected) => updatePropertyData('bedrooms', selected[0] || '')}
                            maxSelections={1}
                          />
                        </div>
                        <div>
                          <Label>Bathrooms</Label>
                          <SelectorChips
                            options={['1', '1.5', '2', '2.5', '3', '3.5', '4', '4+']}
                            onChange={(selected) => updatePropertyData('bathrooms', selected[0] || '')}
                            maxSelections={1}
                          />
                        </div>
                        <div>
                          <Label>Storeys</Label>
                          <SelectorChips
                            options={['1', '2', '3', '4+']}
                            onChange={(selected) => updatePropertyData('storeys', selected[0] || '')}
                            maxSelections={1}
                          />
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="description">Property Description</Label>
                        <Textarea
                          id="description"
                          value={propertyData.description}
                          onChange={(e) => updatePropertyData('description', e.target.value)}
                          placeholder="Welcome to 36 Heron Place - an exceptional 4-bedroom, 3-bathroom family home..."
                          rows={6}
                          className="resize-none"
                        />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Car className="h-5 w-5" />
                        Features & Amenities
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label>Parking Types</Label>
                        <SelectorChips
                          options={[
                            'Assigned Parking', 'Attached Garage', 'Carport', 'Covered Parking',
                            'Detached Garage', 'Driveway', 'Indoor Parking', 'Other Parking',
                            'Parking Lot', 'Parking Pad', 'Parking Stall', 'Street Parking',
                            'Underground', 'Visitor Parking'
                          ]}
                          onChange={(selected) => updatePropertyData('parkingTypes', selected)}
                        />
                      </div>

                      <div>
                        <Label>Extra Features</Label>
                        <SelectorChips
                          options={[
                            'Updated Kitchen', 'Hardwood Floors', 'Central Air', 'Finished Basement',
                            'Fenced Yard', 'Modern Appliances', 'Walk-in Closets', 'Fireplace',
                            'Pool', 'Hot Tub', 'Guest Suite', 'Legal Suite', 'Wood Stove',
                            'Games Room', 'Private Entrance', 'Home Theatre', 'Jacuzzi',
                            'Walkout', 'Virtual Tour/Video', 'Wheelchair Accessible'
                          ]}
                          onChange={(selected) => updatePropertyData('extraFeatures', selected)}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

            {/* Photos Tab */}
              <TabsContent value="photos" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Camera className="h-5 w-5" />
                      Property Photos
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="border-2 border-dashed border-border rounded-lg p-8 text-center">
                      <Camera className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">Upload Property Photos</h3>
                      <p className="text-muted-foreground mb-4">
                        Add high-quality photos to showcase your property. First photo will be the main listing image.
                      </p>
                      <Button variant="outline">
                        Choose Photos
                      </Button>
                    </div>

                    <div>
                      <Label htmlFor="virtualTour">Virtual Tour URL (Optional)</Label>
                      <Input
                        id="virtualTour"
                        value={propertyData.virtualTour}
                        onChange={(e) => updatePropertyData('virtualTour', e.target.value)}
                        placeholder="https://example.com/virtual-tour"
                        className="h-9"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Ads Tab */}
              <TabsContent value="ads" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Listing Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="mlsNumber">MLS® Number (Optional)</Label>
                        <Input
                          id="mlsNumber"
                          value={propertyData.mlsNumber}
                          onChange={(e) => updatePropertyData('mlsNumber', e.target.value)}
                          placeholder="W1234567"
                          className="h-9"
                        />
                      </div>
                      <div>
                        <Label htmlFor="listingAgent">Listing Agent</Label>
                        <Input
                          id="listingAgent"
                          value={propertyData.listingAgent}
                          onChange={(e) => updatePropertyData('listingAgent', e.target.value)}
                          placeholder="John Smith, Real Estate Agent"
                          className="h-9"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="contactInfo">Contact Information</Label>
                      <Textarea
                        id="contactInfo"
                        value={propertyData.contactInfo}
                        onChange={(e) => updatePropertyData('contactInfo', e.target.value)}
                        placeholder="Phone: (*************&#10;Email: <EMAIL>&#10;Brokerage: ABC Realty Inc."
                        rows={4}
                        className="resize-none"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Preview Tab */}
              <TabsContent value="preview" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="h-5 w-5" />
                      Property Details Preview
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <h2 className="text-2xl font-bold mb-2">{propertyData.title || 'Property Title'}</h2>
                      <div className="flex items-center gap-2 text-muted-foreground mb-4">
                        <MapPin className="h-4 w-4" />
                        <span>
                          {[propertyData.address, propertyData.city, propertyData.province].filter(Boolean).join(', ') || 'Property Address'}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 border border-border rounded-lg">
                        <div className="text-2xl font-bold text-primary">{propertyData.propertyType || 'Type'}</div>
                        <div className="text-sm text-muted-foreground">Property Type</div>
                      </div>
                      <div className="text-center p-4 border border-border rounded-lg">
                        <div className="text-2xl font-bold text-primary">${propertyData.price || '0'}</div>
                        <div className="text-sm text-muted-foreground">Price</div>
                      </div>
                      <div className="text-center p-4 border border-border rounded-lg">
                        <div className="text-2xl font-bold text-primary">{propertyData.size || '0'} sq ft</div>
                        <div className="text-sm text-muted-foreground">Size</div>
                      </div>
                      <div className="text-center p-4 border border-border rounded-lg">
                        <div className="text-2xl font-bold text-primary">{propertyData.yearBuilt || 'N/A'}</div>
                        <div className="text-sm text-muted-foreground">Year Built</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center p-4 border border-border rounded-lg">
                        <Bed className="h-6 w-6 mx-auto mb-2 text-primary" />
                        <div className="text-xl font-bold">{propertyData.bedrooms || '0'}</div>
                        <div className="text-sm text-muted-foreground">Bedrooms</div>
                      </div>
                      <div className="text-center p-4 border border-border rounded-lg">
                        <Bath className="h-6 w-6 mx-auto mb-2 text-primary" />
                        <div className="text-xl font-bold">{propertyData.bathrooms || '0'}</div>
                        <div className="text-sm text-muted-foreground">Bathrooms</div>
                      </div>
                      <div className="text-center p-4 border border-border rounded-lg">
                        <Home className="h-6 w-6 mx-auto mb-2 text-primary" />
                        <div className="text-xl font-bold">{propertyData.storeys || '0'}</div>
                        <div className="text-sm text-muted-foreground">Storeys</div>
                      </div>
                    </div>

                    {propertyData.description && (
                      <div>
                        <h3 className="text-lg font-semibold mb-2">Description</h3>
                        <p className="text-muted-foreground leading-relaxed">{propertyData.description}</p>
                      </div>
                    )}

                    {(propertyData.parkingTypes.length > 0 || propertyData.extraFeatures.length > 0) && (
                      <div>
                        <h3 className="text-lg font-semibold mb-3">Features</h3>
                        <div className="space-y-3">
                          {propertyData.parkingTypes.length > 0 && (
                            <div>
                              <h4 className="font-medium mb-2">Parking</h4>
                              <div className="flex flex-wrap gap-2">
                                {propertyData.parkingTypes.map((type, index) => (
                                  <Badge key={index} variant="secondary">{type}</Badge>
                                ))}
                              </div>
                            </div>
                          )}
                          {propertyData.extraFeatures.length > 0 && (
                            <div>
                              <h4 className="font-medium mb-2">Amenities</h4>
                              <div className="flex flex-wrap gap-2">
                                {propertyData.extraFeatures.map((feature, index) => (
                                  <Badge key={index} variant="secondary">{feature}</Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-6 border-t border-border mt-8">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="h-9 px-4"
              >
                Cancel
              </Button>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={handleSaveDraft}
                  disabled={isSaving || !isSignedIn}
                  className="h-9 px-4"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? 'Saving...' : 'Save Draft'}
                </Button>

                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting || !isSignedIn}
                  className="h-9 px-4"
                >
                  {isSubmitting ? 'Publishing...' : 'Publish Listing'}
                </Button>
              </div>
            </div>
        </div>
      </div>

      {/* Loading Overlay */}
      {isSubmitting && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <p className="text-sm">Publishing your property listing...</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
