'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Save,
  Sparkles,
  Home,
  MapPin,
  DollarSign,
  Bed,
  Bath,
  Car,
  Calendar,
  Ruler,
  Camera,
  FileText,
  Eye,
  ArrowLeft,
  Building,
  Settings,
  Image,
  ShoppingCart,
  File
} from 'lucide-react';
import { SelectorChips } from '@/components/ui/selector-chips';

// Comprehensive Property Features
const PROPERTY_FEATURES = [
  'Brick Finishes', 'Walk-in Closet', 'Hardwood Floors', 'Recessed Lighting', 'Dining Room',
  'Fireplace', 'Quartz Countertops', 'Landscaped', 'Parking', 'Stainless Steel',
  'Curb Appeal', 'Laundry', 'Ceiling Fan', 'Custom Cabinetry', 'Garage',
  'Double Sink', 'Spa', 'Outdoor Lighting', 'Quiet Area', 'Beachfront',
  'Ocean View', 'Lake View', 'Mountain View', 'Park View', 'Fenced',
  'Patio', 'Outdoor Fire', 'Hot Tub', 'Pool', 'Irrigation System',
  'Fruit Trees', 'Garden', 'Basketball Court', 'Tennis Court', 'Solar Panels',
  'RV or Boat Parking', 'Dock', 'Rooftop Deck', 'Asphalt Roof', 'Water Feature',
  'Pergola', 'Stone Finishes', 'Stucco Finishes', 'Wood Siding', 'Vinyl Siding',
  'Concrete Features', 'Glass Features', 'Facade', 'Log Siding', 'Outdoor Storage',
  'Corner Lot', 'Roof Condition', 'Walkways', 'Chimney', 'Outdoor Kitchen',
  'Outdoor Dining Area', 'Playground Equipment', 'Vegetable Garden', 'Side Yard',
  'Garden Beds', 'Flower Beds', 'BBQ Area', 'Outdoor Pizza Oven', 'Outdoor Shower',
  'Carport', 'Architectural Style', 'Painted Exterior', 'Driveway Gate',
  'Apartment Stories', 'Stamped Concrete', 'Outdoor Ceiling Fans', 'Gravel Driveway',
  'Outdoor Speakers', 'Vineyard', 'Shopping & Dining', 'Nearby Schools',
  'Neighbourhood Amenities', 'Skylights', 'Open Floor Plan', 'Vinyl Floors',
  'Vaulted Ceilings', 'Smart Home', 'Central Vac', 'High Ceilings',
  'Air Conditioning', 'Heating System', 'Crown Molding', 'Jacuzzi Tub',
  'Wine Cellar', 'Home Office', 'Exercise Room', 'Media Room',
  'Home Theatre', 'Sauna', 'Wet Bar', 'Sunroom', 'Rec Room',
  'Den', 'Yoga Studio', 'Library', 'Ensuite Bathroom', 'Storage',
  'Granite Countertops', 'Marble Countertops', 'Pantry', 'Kitchen Island',
  'Renovated', 'Gas Range', 'Wine Fridge', 'Soft Close Drawers',
  'Carpet Flooring', 'Tile Flooring', 'Heated Floors', 'Laminate Flooring',
  'Ice Maker', 'Dishwasher', 'Smart Appliances', 'Elevator', 'In-law Suite',
  'French Doors', 'Built-in Shelving', 'Mudroom', 'Loft Area',
  'Picture Windows', 'Breakfast Nook', 'Convection Oven', 'Chefs Kitchen',
  'Built-in Microwave', 'Double Oven', 'Walk-in Pantry', 'Water Filtration System',
  'Guest Bedrooms', 'Paint Details', 'Window Coverings', 'Staircase',
  'Interior Columns', 'Brick or Stone Accents', 'Architectural Details',
  'Energy Efficiency', 'Track Lighting', 'Bi-Fold Doors', 'Hot Water Tank',
  'Pocket Doors', 'Chandeliers', 'Transom Windows', 'Pendant Lighting',
  'Tray Ceilings', 'Exposed Brick Walls', 'Dimmer Switches', 'Home Automation System',
  'Security System', 'Glass Doors', 'Backsplash', 'Pot Filler',
  'Induction Cooktop', 'Farmhouse Sink', 'Glass Front Cabinets', 'Under-Cabinet Lighting',
  'Wine Rack', 'Kitchen Desk', 'Pantry Shelving', 'Pot Rack',
  'Range Hood', 'Professional-Grade Appliances', 'Double Islands', 'Scullery',
  'Integrated Appliances', 'Guest Bathroom', 'His and Hers Closets', 'Mirrored Closet Doors',
  'Walk-In Shower', 'Vanities', 'Billiard Room', 'Murphy Bed',
  'Attic', 'Grand Foyer', 'Built-In Oven', 'Legal Suite'
];

// Security Features Options
const SECURITY_FEATURES = [
  'Security System', 'Gated Community', 'Security Cameras', 'Motion Sensors',
  'Alarm System', 'Intercom System', 'Keypad Entry', 'Smart Locks',
  'Security Lighting', 'Fence', 'Guard House', 'Access Control'
];

// Appliances Included Options
const APPLIANCES_INCLUDED = [
  'Refrigerator', 'Dishwasher', 'Stove/Oven', 'Microwave', 'Washer',
  'Dryer', 'Wine Fridge', 'Ice Maker', 'Garbage Disposal', 'Range Hood',
  'Built-in Vacuum', 'Water Heater', 'Air Conditioning', 'Heating System'
];

// Sidebar Navigation Items
const SIDEBAR_SECTIONS = [
  {
    id: 'information',
    label: 'Property Information',
    icon: Building,
    color: 'bg-blue-500',
    count: 8
  },
  {
    id: 'specs',
    label: 'Property Specs',
    icon: Settings,
    color: 'bg-purple-500',
    count: 35
  },
  {
    id: 'media',
    label: 'Property Media',
    icon: Image,
    color: 'bg-green-500',
    count: 2
  },
  {
    id: 'marketplace',
    label: 'Ads Marketplace',
    icon: ShoppingCart,
    color: 'bg-orange-500',
    count: 3
  },
  {
    id: 'documents',
    label: 'Property Documents',
    icon: File,
    color: 'bg-red-500',
    count: 1
  }
];

interface PropertyData {
  // Property Information
  title: string;
  description: string;
  address: string;
  city: string;
  province: string;
  postalCode: string;
  propertyType: string;
  adType: string;

  // Property Specs
  yearBuilt: string;
  landSize: string;
  interiorSize: string;
  askingPrice: string;
  listing: string;
  leasePrice: string;
  bedrooms: string;
  bathrooms: string;
  halfBathrooms: string;
  currency: string;
  leaseTerms: string;
  parkingSpaces: string;
  heating: string;
  cooling: string;
  basement: string;
  roof: string;
  hoa: string;
  tax: string;
  schoolDistrict: string;
  walkScore: string;
  transitScore: string;
  bikeScore: string;
  petPolicy: string;
  viewType: string;
  energyEfficiencyRating: string;
  dimensions: string;
  waterfront: string;
  securityFeatures: string[];
  appliancesIncluded: string[];
  ceilingHeight: string;
  foundationType: string;
  sanitarySystem: string;
  internetAvailable: string;
  sunlightExposure: string;
  communityEvents: string;
  internetSpeeds: string;
  energyCosts: string;
  waterNearby: string;
  airportsNearby: string;
  emergencyServicesNearby: string;
  educationalServicesNearby: string;
  portfolio: string;
  propertyShortSummary: string;
  propertyHeadline: string;

  // Property Features
  propertyFeatures: string[];

  // Property Media
  virtualTour: string;
  photos: string[];

  // Ads Marketplace
  mlsNumber: string;
  listingAgent: string;
  contactInfo: string;

  // Property Documents
  documents: string[];
}

export function PropertyListingForm() {
  const { user, isSignedIn } = useUser();
  const router = useRouter();
  const [activeSection, setActiveSection] = useState('information');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [showAIImport, setShowAIImport] = useState(false);
  const [aiAddress, setAiAddress] = useState('');
  const [isImporting, setIsImporting] = useState(false);

  const [propertyData, setPropertyData] = useState<PropertyData>({
    // Property Information
    title: '',
    description: '',
    address: '',
    city: '',
    province: '',
    postalCode: '',
    propertyType: '',
    adType: '',

    // Property Specs
    yearBuilt: '',
    landSize: '',
    interiorSize: '',
    askingPrice: '',
    listing: '',
    leasePrice: '',
    bedrooms: '',
    bathrooms: '',
    halfBathrooms: '',
    currency: 'CAD',
    leaseTerms: '',
    parkingSpaces: '',
    heating: '',
    cooling: '',
    basement: '',
    roof: '',
    hoa: '',
    tax: '',
    schoolDistrict: '',
    walkScore: '',
    transitScore: '',
    bikeScore: '',
    petPolicy: '',
    viewType: '',
    energyEfficiencyRating: '',
    dimensions: '',
    waterfront: '',
    securityFeatures: [],
    appliancesIncluded: [],
    ceilingHeight: '',
    foundationType: '',
    sanitarySystem: '',
    internetAvailable: '',
    sunlightExposure: '',
    communityEvents: '',
    internetSpeeds: '',
    energyCosts: '',
    waterNearby: '',
    airportsNearby: '',
    emergencyServicesNearby: '',
    educationalServicesNearby: '',
    portfolio: '',
    propertyShortSummary: '',
    propertyHeadline: '',

    // Property Features
    propertyFeatures: [],

    // Property Media
    virtualTour: '',
    photos: [],

    // Ads Marketplace
    mlsNumber: '',
    listingAgent: '',
    contactInfo: '',

    // Property Documents
    documents: [],
  });

  // Auto-save functionality
  useEffect(() => {
    const autoSave = setTimeout(() => {
      if (isSignedIn && Object.values(propertyData).some(value =>
        Array.isArray(value) ? value.length > 0 : value !== ''
      )) {
        handleSaveDraft();
      }
    }, 30000); // Auto-save every 30 seconds

    return () => clearTimeout(autoSave);
  }, [propertyData, isSignedIn]);

  // Load saved draft on component mount
  useEffect(() => {
    if (isSignedIn) {
      loadSavedDraft();
    }
  }, [isSignedIn]);

  const loadSavedDraft = async () => {
    try {
      const saved = localStorage.getItem(`property-draft-${user?.id}`);
      if (saved) {
        const draftData = JSON.parse(saved);
        setPropertyData(draftData.data);
        setLastSaved(new Date(draftData.timestamp));
      }
    } catch (error) {
      console.error('Error loading draft:', error);
    }
  };

  const handleSaveDraft = async () => {
    if (!isSignedIn) return;

    setIsSaving(true);
    try {
      const draftData = {
        data: propertyData,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem(`property-draft-${user?.id}`, JSON.stringify(draftData));
      setLastSaved(new Date());
    } catch (error) {
      console.error('Error saving draft:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleAIImport = async () => {
    if (!aiAddress.trim()) return;

    setIsImporting(true);
    try {
      // Simulate AI import - replace with actual API call
      const response = await fetch('/api/ai-property-import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ address: aiAddress })
      });

      if (response.ok) {
        const aiData = await response.json();
        setPropertyData(prev => ({
          ...prev,
          ...aiData
        }));
        setShowAIImport(false);
        setAiAddress('');
      }
    } catch (error) {
      console.error('Error importing AI data:', error);
    } finally {
      setIsImporting(false);
    }
  };

  const handleSubmit = async () => {
    if (!isSignedIn) {
      router.push('/sign-in?redirect=/list-property');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/property-listings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(propertyData)
      });

      if (response.ok) {
        // Clear draft after successful submission
        localStorage.removeItem(`property-draft-${user?.id}`);
        router.push('/dashboard/properties?success=listing-created');
      } else {
        throw new Error('Failed to submit listing');
      }
    } catch (error) {
      console.error('Error submitting listing:', error);
      alert('Error submitting listing. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const updatePropertyData = (field: keyof PropertyData, value: any) => {
    setPropertyData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Helper functions for progress tracking
  const getCompletedFieldsCount = (sectionId: string): number => {
    switch (sectionId) {
      case 'information':
        return [
          propertyData.title, propertyData.description, propertyData.address,
          propertyData.city, propertyData.province, propertyData.postalCode,
          propertyData.propertyType, propertyData.adType
        ].filter(field => field && field.trim() !== '').length;

      case 'specs':
        return [
          propertyData.yearBuilt, propertyData.landSize, propertyData.interiorSize,
          propertyData.askingPrice, propertyData.bedrooms, propertyData.bathrooms,
          propertyData.halfBathrooms, propertyData.parkingSpaces, propertyData.heating,
          propertyData.cooling, propertyData.basement, propertyData.roof
        ].filter(field => field && field.trim() !== '').length +
          (propertyData.securityFeatures.length > 0 ? 1 : 0) +
          (propertyData.appliancesIncluded.length > 0 ? 1 : 0) +
          (propertyData.propertyFeatures.length > 0 ? 1 : 0);

      case 'media':
        return [propertyData.virtualTour].filter(field => field && field.trim() !== '').length +
          (propertyData.photos.length > 0 ? 1 : 0);

      case 'marketplace':
        return [propertyData.mlsNumber, propertyData.listingAgent, propertyData.contactInfo]
          .filter(field => field && field.trim() !== '').length;

      case 'documents':
        return propertyData.documents.length > 0 ? 1 : 0;

      default:
        return 0;
    }
  };

  const getTotalFieldsCount = (sectionId: string): number => {
    switch (sectionId) {
      case 'information': return 8;
      case 'specs': return 15;
      case 'media': return 2;
      case 'marketplace': return 3;
      case 'documents': return 1;
      default: return 0;
    }
  };

  return (
    <div className="w-full min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-40">
        <div className="w-full px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="h-8 px-2"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back
              </Button>
              <div className="flex items-center gap-2">
                <Home className="h-5 w-5 text-primary" />
                <h1 className="text-xl font-semibold">List Your Property</h1>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {lastSaved && (
                <span className="text-xs text-muted-foreground">
                  Last saved: {lastSaved.toLocaleTimeString()}
                </span>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={handleSaveDraft}
                disabled={isSaving || !isSignedIn}
                className="h-8 px-2"
              >
                <Save className="h-4 w-4 mr-1" />
                {isSaving ? 'Saving...' : 'Save Draft'}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAIImport(!showAIImport)}
                className="h-8 px-2 bg-gradient-to-r from-orange-50 to-green-50 border-orange-200"
              >
                <Sparkles className="h-4 w-4 mr-1 text-orange-500" />
                AI Import
              </Button>
            </div>
          </div>

          {/* AI Import Section */}
          {showAIImport && (
            <div className="mt-3 p-3 border border-orange-200 rounded-lg bg-gradient-to-r from-orange-50/50 to-green-50/50">
              <div className="flex items-center gap-2 mb-2">
                <Sparkles className="h-4 w-4 text-orange-500" />
                <span className="text-sm font-medium">Import Property Data with AI</span>
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="Enter property address (e.g., 123 Main St, Toronto, ON)"
                  value={aiAddress}
                  onChange={(e) => setAiAddress(e.target.value)}
                  className="flex-1 h-8"
                />
                <Button
                  size="sm"
                  onClick={handleAIImport}
                  disabled={isImporting || !aiAddress.trim()}
                  className="h-8 px-3"
                >
                  {isImporting ? 'Importing...' : 'Import'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full flex">
        {/* Left Sidebar Navigation */}
        <div className="w-80 border-r border-border bg-card/50 p-4">
          <ScrollArea className="h-[calc(100vh-120px)]">
            <div className="space-y-2">
              {SIDEBAR_SECTIONS.map((section) => {
                const Icon = section.icon;
                const isActive = activeSection === section.id;
                const completedFields = getCompletedFieldsCount(section.id);
                const totalFields = getTotalFieldsCount(section.id);
                const progress = totalFields > 0 ? (completedFields / totalFields) * 100 : 0;

                return (
                  <div key={section.id} className="space-y-1">
                    <button
                      onClick={() => setActiveSection(section.id)}
                      className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors ${isActive
                        ? 'bg-primary/10 border border-primary/20 text-primary'
                        : 'hover:bg-muted/50 text-muted-foreground hover:text-foreground'
                        }`}
                    >
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${section.color} text-white`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm">{section.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {completedFields}/{totalFields} completed
                        </div>
                      </div>
                      <div className="text-xs font-medium">
                        {Math.round(progress)}%
                      </div>
                    </button>

                    {/* Progress Bar */}
                    <div className="ml-11 mr-3">
                      <div className="w-full bg-muted rounded-full h-1">
                        <div
                          className={`h-1 rounded-full transition-all duration-300 ${section.color}`}
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Save Section Button */}
            <div className="mt-6 pt-4 border-t border-border">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSaveDraft()}
                disabled={isSaving || !isSignedIn}
                className="w-full h-8"
              >
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? 'Saving Section...' : 'Save Current Section'}
              </Button>
            </div>
          </ScrollArea>
        </div>

        {/* Right Panel - Form Content */}
        <div className="flex-1 p-6">
          <ScrollArea className="h-[calc(100vh-120px)]">
            <div className="space-y-4">

              {/* Property Information Section */}
              {activeSection === 'information' && (
                <div className="space-y-3">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <Building className="h-5 w-5" />
                        Property Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {/* Property Title */}
                      <div>
                        <Label htmlFor="title" className="text-sm font-medium">Property Title</Label>
                        <Input
                          id="title"
                          value={propertyData.title}
                          onChange={(e) => updatePropertyData('title', e.target.value)}
                          placeholder="Beautiful Family Home in Prime Location"
                          className="h-8 mt-1"
                        />
                      </div>

                      {/* Property Description */}
                      <div>
                        <Label htmlFor="description" className="text-sm font-medium">Property Description</Label>
                        <Textarea
                          id="description"
                          value={propertyData.description}
                          onChange={(e) => updatePropertyData('description', e.target.value)}
                          placeholder="Describe your property's key features, location benefits, and unique selling points..."
                          rows={3}
                          className="resize-none mt-1"
                        />
                      </div>

                      {/* Location Fields */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div className="md:col-span-2">
                          <Label htmlFor="address" className="text-sm font-medium">Street Address</Label>
                          <Input
                            id="address"
                            value={propertyData.address}
                            onChange={(e) => updatePropertyData('address', e.target.value)}
                            placeholder="123 Main Street"
                            className="h-8 mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="city" className="text-sm font-medium">City</Label>
                          <Input
                            id="city"
                            value={propertyData.city}
                            onChange={(e) => updatePropertyData('city', e.target.value)}
                            placeholder="Toronto"
                            className="h-8 mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="province" className="text-sm font-medium">Province</Label>
                          <Select value={propertyData.province} onValueChange={(value) => updatePropertyData('province', value)}>
                            <SelectTrigger className="h-8 mt-1">
                              <SelectValue placeholder="Select province" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="AB">Alberta</SelectItem>
                              <SelectItem value="BC">British Columbia</SelectItem>
                              <SelectItem value="MB">Manitoba</SelectItem>
                              <SelectItem value="NB">New Brunswick</SelectItem>
                              <SelectItem value="NL">Newfoundland and Labrador</SelectItem>
                              <SelectItem value="NS">Nova Scotia</SelectItem>
                              <SelectItem value="ON">Ontario</SelectItem>
                              <SelectItem value="PE">Prince Edward Island</SelectItem>
                              <SelectItem value="QC">Quebec</SelectItem>
                              <SelectItem value="SK">Saskatchewan</SelectItem>
                              <SelectItem value="NT">Northwest Territories</SelectItem>
                              <SelectItem value="NU">Nunavut</SelectItem>
                              <SelectItem value="YT">Yukon</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="postalCode" className="text-sm font-medium">Postal Code</Label>
                          <Input
                            id="postalCode"
                            value={propertyData.postalCode}
                            onChange={(e) => updatePropertyData('postalCode', e.target.value)}
                            placeholder="M5V 3A8"
                            className="h-8 mt-1"
                          />
                        </div>
                      </div>

                      {/* Property Type and Ad Type */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor="propertyType" className="text-sm font-medium">Property Type</Label>
                          <Select value={propertyData.propertyType} onValueChange={(value) => updatePropertyData('propertyType', value)}>
                            <SelectTrigger className="h-8 mt-1">
                              <SelectValue placeholder="Select property type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="House">House</SelectItem>
                              <SelectItem value="Condo">Condo</SelectItem>
                              <SelectItem value="Townhouse">Townhouse</SelectItem>
                              <SelectItem value="Land">Land</SelectItem>
                              <SelectItem value="Commercial">Commercial</SelectItem>
                              <SelectItem value="Industrial">Industrial</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="adType" className="text-sm font-medium">Ad Type</Label>
                          <Select value={propertyData.adType} onValueChange={(value) => updatePropertyData('adType', value)}>
                            <SelectTrigger className="h-8 mt-1">
                              <SelectValue placeholder="Select ad type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Sale">For Sale</SelectItem>
                              <SelectItem value="Rent">For Rent</SelectItem>
                              <SelectItem value="Lease">For Lease</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Property Specs Section */}
              {activeSection === 'specs' && (
                <div className="space-y-3">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <Settings className="h-5 w-5" />
                        Property Specs
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {/* Basic Property Details */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        <div>
                          <Label htmlFor="yearBuilt" className="text-sm font-medium">Year Built</Label>
                          <Input
                            id="yearBuilt"
                            value={propertyData.yearBuilt}
                            onChange={(e) => updatePropertyData('yearBuilt', e.target.value)}
                            placeholder="2010"
                            className="h-8 mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="landSize" className="text-sm font-medium">Land Size (Acres)</Label>
                          <Input
                            id="landSize"
                            value={propertyData.landSize}
                            onChange={(e) => updatePropertyData('landSize', e.target.value)}
                            placeholder="0.25"
                            className="h-8 mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="interiorSize" className="text-sm font-medium">Interior Size (Sqft)</Label>
                          <Input
                            id="interiorSize"
                            value={propertyData.interiorSize}
                            onChange={(e) => updatePropertyData('interiorSize', e.target.value)}
                            placeholder="2100"
                            className="h-8 mt-1"
                          />
                        </div>
                      </div>

                      {/* Pricing */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        <div>
                          <Label htmlFor="askingPrice" className="text-sm font-medium">Asking Price</Label>
                          <Input
                            id="askingPrice"
                            value={propertyData.askingPrice}
                            onChange={(e) => updatePropertyData('askingPrice', e.target.value)}
                            placeholder="750,000"
                            className="h-8 mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="leasePrice" className="text-sm font-medium">Lease Price</Label>
                          <Input
                            id="leasePrice"
                            value={propertyData.leasePrice}
                            onChange={(e) => updatePropertyData('leasePrice', e.target.value)}
                            placeholder="3,500"
                            className="h-8 mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="currency" className="text-sm font-medium">Currency</Label>
                          <Select value={propertyData.currency} onValueChange={(value) => updatePropertyData('currency', value)}>
                            <SelectTrigger className="h-8 mt-1">
                              <SelectValue placeholder="Select currency" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="CAD">CAD</SelectItem>
                              <SelectItem value="USD">USD</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {/* Room Details */}
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
                        <div>
                          <Label className="text-sm font-medium">Bedrooms</Label>
                          <SelectorChips
                            options={['1', '2', '3', '4', '5', '6+']}
                            selected={propertyData.bedrooms ? [propertyData.bedrooms] : []}
                            onChange={(selected) => updatePropertyData('bedrooms', selected[0] || '')}
                            maxSelections={1}
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label className="text-sm font-medium">Bathrooms</Label>
                          <SelectorChips
                            options={['1', '1.5', '2', '2.5', '3', '3.5', '4', '4+']}
                            selected={propertyData.bathrooms ? [propertyData.bathrooms] : []}
                            onChange={(selected) => updatePropertyData('bathrooms', selected[0] || '')}
                            maxSelections={1}
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label className="text-sm font-medium">Half Bathrooms</Label>
                          <SelectorChips
                            options={['0', '1', '2', '3+']}
                            selected={propertyData.halfBathrooms ? [propertyData.halfBathrooms] : []}
                            onChange={(selected) => updatePropertyData('halfBathrooms', selected[0] || '')}
                            maxSelections={1}
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="parkingSpaces" className="text-sm font-medium">Parking Spaces</Label>
                          <Input
                            id="parkingSpaces"
                            value={propertyData.parkingSpaces}
                            onChange={(e) => updatePropertyData('parkingSpaces', e.target.value)}
                            placeholder="2"
                            className="h-8 mt-1"
                          />
                        </div>
                      </div>

                      {/* Systems */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor="heating" className="text-sm font-medium">Heating</Label>
                          <Input
                            id="heating"
                            value={propertyData.heating}
                            onChange={(e) => updatePropertyData('heating', e.target.value)}
                            placeholder="Gas, Forced Air"
                            className="h-8 mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="cooling" className="text-sm font-medium">Cooling</Label>
                          <Input
                            id="cooling"
                            value={propertyData.cooling}
                            onChange={(e) => updatePropertyData('cooling', e.target.value)}
                            placeholder="Central Air"
                            className="h-8 mt-1"
                          />
                        </div>
                      </div>

                      {/* Property Features */}
                      <div>
                        <Label className="text-sm font-medium">Property Features</Label>
                        <SelectorChips
                          options={PROPERTY_FEATURES}
                          selected={propertyData.propertyFeatures}
                          onChange={(selected) => updatePropertyData('propertyFeatures', selected)}
                          className="mt-1"
                        />
                      </div>

                      {/* Security Features */}
                      <div>
                        <Label className="text-sm font-medium">Security Features</Label>
                        <SelectorChips
                          options={SECURITY_FEATURES}
                          selected={propertyData.securityFeatures}
                          onChange={(selected) => updatePropertyData('securityFeatures', selected)}
                          className="mt-1"
                        />
                      </div>

                      {/* Appliances Included */}
                      <div>
                        <Label className="text-sm font-medium">Appliances Included</Label>
                        <SelectorChips
                          options={APPLIANCES_INCLUDED}
                          selected={propertyData.appliancesIncluded}
                          onChange={(selected) => updatePropertyData('appliancesIncluded', selected)}
                          className="mt-1"
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Property Media Section */}
              {activeSection === 'media' && (
                <div className="space-y-3">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <Image className="h-5 w-5" />
                        Property Media
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {/* Photo Upload */}
                      <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                        <Camera className="h-10 w-10 mx-auto text-muted-foreground mb-3" />
                        <h3 className="text-base font-medium mb-2">Upload Property Photos</h3>
                        <p className="text-sm text-muted-foreground mb-3">
                          Add high-quality photos to showcase your property. First photo will be the main listing image.
                        </p>
                        <Button variant="outline" size="sm" className="h-8">
                          Choose Photos
                        </Button>
                      </div>

                      {/* Virtual Tour */}
                      <div>
                        <Label htmlFor="virtualTour" className="text-sm font-medium">Virtual Tour URL (Optional)</Label>
                        <Input
                          id="virtualTour"
                          value={propertyData.virtualTour}
                          onChange={(e) => updatePropertyData('virtualTour', e.target.value)}
                          placeholder="https://example.com/virtual-tour"
                          className="h-8 mt-1"
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Ads Marketplace Section */}
              {activeSection === 'marketplace' && (
                <div className="space-y-3">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <ShoppingCart className="h-5 w-5" />
                        Ads Marketplace
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor="mlsNumber" className="text-sm font-medium">MLS® Number (Optional)</Label>
                          <Input
                            id="mlsNumber"
                            value={propertyData.mlsNumber}
                            onChange={(e) => updatePropertyData('mlsNumber', e.target.value)}
                            placeholder="W1234567"
                            className="h-8 mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="listingAgent" className="text-sm font-medium">Listing Agent</Label>
                          <Input
                            id="listingAgent"
                            value={propertyData.listingAgent}
                            onChange={(e) => updatePropertyData('listingAgent', e.target.value)}
                            placeholder="John Smith, Real Estate Agent"
                            className="h-8 mt-1"
                          />
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="contactInfo" className="text-sm font-medium">Contact Information</Label>
                        <Textarea
                          id="contactInfo"
                          value={propertyData.contactInfo}
                          onChange={(e) => updatePropertyData('contactInfo', e.target.value)}
                          placeholder="Phone: (*************&#10;Email: <EMAIL>&#10;Brokerage: ABC Realty Inc."
                          rows={3}
                          className="resize-none mt-1"
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Property Documents Section */}
              {activeSection === 'documents' && (
                <div className="space-y-3">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <File className="h-5 w-5" />
                        Property Documents
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                        <File className="h-10 w-10 mx-auto text-muted-foreground mb-3" />
                        <h3 className="text-base font-medium mb-2">Upload Property Documents</h3>
                        <p className="text-sm text-muted-foreground mb-3">
                          Add relevant documents such as floor plans, property surveys, or inspection reports.
                        </p>
                        <Button variant="outline" size="sm" className="h-8">
                          Choose Documents
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-between items-center pt-6 border-t border-border mt-8">
                <Button
                  variant="outline"
                  onClick={() => router.back()}
                  className="h-8 px-4"
                >
                  Cancel
                </Button>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={handleSaveDraft}
                    disabled={isSaving || !isSignedIn}
                    className="h-8 px-4"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isSaving ? 'Saving...' : 'Save Draft'}
                  </Button>

                  <Button
                    onClick={handleSubmit}
                    disabled={isSubmitting || !isSignedIn}
                    className="h-8 px-4"
                  >
                    {isSubmitting ? 'Publishing...' : 'Publish Listing'}
                  </Button>
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>
      </div>

      {/* Loading Overlay */}
      {isSubmitting && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <p className="text-sm">Publishing your property listing...</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
