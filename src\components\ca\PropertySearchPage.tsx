'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import {
  Search,
  MapPin,
  Filter,
  Calendar,
  Home,
  Bed,
  Bath,
  Car,
  Sparkles,
  SlidersHorizontal,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useSearchOptional } from '@/contexts/SearchContext'

interface PropertySearchPageProps {
  userType: 'buyer' | 'seller'
  onBack?: () => void
}

export function PropertySearchPage({ userType, onBack }: PropertySearchPageProps) {
  const searchContext = useSearchOptional()
  const [searchQuery, setSearchQuery] = useState('')
  const [mlsNumber, setMlsNumber] = useState('')
  const [propertyType, setPropertyType] = useState('')
  const [adType, setAdType] = useState('')
  const [priceMin, setPriceMin] = useState('')
  const [priceMax, setPriceMax] = useState('')
  const [yearBuiltMin, setYearBuiltMin] = useState('')
  const [yearBuiltMax, setYearBuiltMax] = useState('')
  const [beds, setBeds] = useState('')
  const [baths, setBaths] = useState('')
  const [storeys, setStoreys] = useState('')
  const [parkingTypes, setParkingTypes] = useState<string[]>([])
  const [extraFeatures, setExtraFeatures] = useState<string[]>([])
  const [showAdvanced, setShowAdvanced] = useState(false)

  // Prefill form with search context data
  useEffect(() => {
    if (searchContext?.searchParams) {
      const { searchParams } = searchContext
      setSearchQuery(searchParams.searchQuery || '')
      setMlsNumber(searchParams.mlsNumber || '')
      setPropertyType(searchParams.propertyType || '')
      setAdType(searchParams.adType || '')
      setPriceMin(searchParams.priceMin || '')
      setPriceMax(searchParams.priceMax || '')
      setYearBuiltMin(searchParams.yearBuiltMin || '')
      setYearBuiltMax(searchParams.yearBuiltMax || '')
      setBeds(searchParams.beds || '')
      setBaths(searchParams.baths || '')
      setStoreys(searchParams.storeys || '')
      setParkingTypes(searchParams.parkingTypes || [])
      setExtraFeatures(searchParams.extraFeatures || [])
    }
  }, [searchContext])

  const parkingOptions = [
    'Assigned Parking', 'Attached Garage', 'Carport', 'Covered Parking',
    'Detached Garage', 'Driveway', 'Indoor Parking', 'Other Parking',
    'Parking Lot', 'Parking Pad', 'Parking Stall', 'Street Parking',
    'Underground', 'Visitor Parking'
  ]

  const extraFeaturesOptions = [
    'Wheelchair Accessible', 'Hot Tub', 'Guest Suite', 'MLS® System',
    'Fireplace', 'Pool', 'Legal Suite', 'Wood Stove', 'Games Room',
    'Private Entrance', 'A/C', 'Home Theatre', 'Open House',
    'Jacuzzi', 'Walkout', 'Virtual Tour/Video'
  ]

  const handleParkingChange = (parking: string, checked: boolean) => {
    if (checked) {
      setParkingTypes([...parkingTypes, parking])
    } else {
      setParkingTypes(parkingTypes.filter(p => p !== parking))
    }
  }

  const handleFeatureChange = (feature: string, checked: boolean) => {
    if (checked) {
      setExtraFeatures([...extraFeatures, feature])
    } else {
      setExtraFeatures(extraFeatures.filter(f => f !== feature))
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-accent/5 to-background py-8 relative overflow-hidden">
      {/* Background Glass Effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-3/4 left-1/2 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Back Button */}
        {onBack && (
          <div className="mb-8">
            <Button
              variant="outline"
              onClick={onBack}
              className="bg-background/60 backdrop-blur-xl border-white/20 hover:bg-background/80"
            >
              ← Back to Dashboard
            </Button>
          </div>
        )}

        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="p-3 rounded-2xl bg-gradient-to-br from-emerald-500/20 to-teal-500/20 backdrop-blur">
              <Search className="h-7 w-7 text-emerald-600" />
            </div>
            <Badge variant="outline" className="text-sm bg-background/60 backdrop-blur border-emerald-200/50 text-emerald-700">
              🇨🇦 MLS® Property Search
            </Badge>
          </div>
          <h1 className="text-4xl lg:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-foreground via-emerald-600 to-teal-600 bg-clip-text text-transparent">
              Advanced Property Search
            </span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Search over <span className="font-semibold text-emerald-600">15,000+ active listings</span> across Canada with our comprehensive MLS® integration and advanced filtering system
          </p>

          {/* Quick Search Stats */}
          <div className="flex items-center justify-center gap-8 mt-8 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
              <span>Real-time MLS® data</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span>Commission-free listings</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
              <span>Verified properties</span>
            </div>
          </div>
        </div>

        {/* Main Search Card */}
        <Card className="max-w-6xl mx-auto bg-background/80 backdrop-blur-xl border border-white/20 shadow-2xl">
          <CardHeader className="pb-6">
            <CardTitle className="flex items-center gap-2 text-2xl">
              <Search className="h-6 w-6 text-accent" />
              Property Search
            </CardTitle>
          </CardHeader>

          <CardContent className="space-y-8">
            {/* Primary Search */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="location" className="text-sm font-medium">
                  City, Province
                </Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="location"
                    placeholder="Enter city, province, or postal code..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 h-12 bg-background/50 backdrop-blur border-white/20"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="mls" className="text-sm font-medium">
                  MLS# or Listing ID#
                </Label>
                <Input
                  id="mls"
                  placeholder="Enter MLS# or Listing ID..."
                  value={mlsNumber}
                  onChange={(e) => setMlsNumber(e.target.value)}
                  className="h-12 bg-background/50 backdrop-blur border-white/20"
                />
              </div>
            </div>

            {/* Basic Filters */}
            <div className="grid md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Property Type</Label>
                <Select value={propertyType} onValueChange={setPropertyType}>
                  <SelectTrigger className="h-12 bg-background/50 backdrop-blur border-white/20">
                    <SelectValue placeholder="Any" />
                  </SelectTrigger>
                  <SelectContent className="bg-background/95 backdrop-blur border-white/20">
                    <SelectItem value="house">House</SelectItem>
                    <SelectItem value="condo">Condo</SelectItem>
                    <SelectItem value="townhouse">Townhouse</SelectItem>
                    <SelectItem value="apartment">Apartment</SelectItem>
                    <SelectItem value="land">Land</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Ad Type</Label>
                <Select value={adType} onValueChange={setAdType}>
                  <SelectTrigger className="h-12 bg-background/50 backdrop-blur border-white/20">
                    <SelectValue placeholder="Any" />
                  </SelectTrigger>
                  <SelectContent className="bg-background/95 backdrop-blur border-white/20">
                    <SelectItem value="sale">For Sale</SelectItem>
                    <SelectItem value="sold">Sold</SelectItem>
                    <SelectItem value="delisted">Delisted</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Beds</Label>
                <Select value={beds} onValueChange={setBeds}>
                  <SelectTrigger className="h-12 bg-background/50 backdrop-blur border-white/20">
                    <SelectValue placeholder="Any" />
                  </SelectTrigger>
                  <SelectContent className="bg-background/95 backdrop-blur border-white/20">
                    <SelectItem value="1">1+</SelectItem>
                    <SelectItem value="2">2+</SelectItem>
                    <SelectItem value="3">3+</SelectItem>
                    <SelectItem value="4">4+</SelectItem>
                    <SelectItem value="5">5+</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Baths</Label>
                <Select value={baths} onValueChange={setBaths}>
                  <SelectTrigger className="h-12 bg-background/50 backdrop-blur border-white/20">
                    <SelectValue placeholder="Any" />
                  </SelectTrigger>
                  <SelectContent className="bg-background/95 backdrop-blur border-white/20">
                    <SelectItem value="1">1+</SelectItem>
                    <SelectItem value="2">2+</SelectItem>
                    <SelectItem value="3">3+</SelectItem>
                    <SelectItem value="4">4+</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Price Range */}
            <div className="space-y-4">
              <Label className="text-sm font-medium">Price Range</Label>
              <div className="grid md:grid-cols-2 gap-4">
                <Input
                  placeholder="Min Price"
                  value={priceMin}
                  onChange={(e) => setPriceMin(e.target.value)}
                  className="h-12 bg-background/50 backdrop-blur border-white/20"
                />
                <Input
                  placeholder="Max Price"
                  value={priceMax}
                  onChange={(e) => setPriceMax(e.target.value)}
                  className="h-12 bg-background/50 backdrop-blur border-white/20"
                />
              </div>
            </div>

            {/* Advanced Filters Toggle */}
            <div className="flex items-center justify-center">
              <Button
                variant="outline"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="bg-background/50 backdrop-blur border-white/20 hover:bg-accent/10"
              >
                <SlidersHorizontal className="mr-2 h-4 w-4" />
                {showAdvanced ? 'Hide' : 'Show'} Advanced Filters
              </Button>
            </div>

            {/* Advanced Filters */}
            {showAdvanced && (
              <div className="space-y-6 p-6 rounded-lg bg-accent/5 border border-accent/20">
                {/* Year Built */}
                <div className="space-y-4">
                  <Label className="text-sm font-medium">Year Built</Label>
                  <div className="grid md:grid-cols-2 gap-4">
                    <Input
                      placeholder="Min Year"
                      value={yearBuiltMin}
                      onChange={(e) => setYearBuiltMin(e.target.value)}
                      className="h-12 bg-background/50 backdrop-blur border-white/20"
                    />
                    <Input
                      placeholder="Max Year"
                      value={yearBuiltMax}
                      onChange={(e) => setYearBuiltMax(e.target.value)}
                      className="h-12 bg-background/50 backdrop-blur border-white/20"
                    />
                  </div>
                </div>

                {/* Storeys */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Storeys</Label>
                  <Select value={storeys} onValueChange={setStoreys}>
                    <SelectTrigger className="h-12 bg-background/50 backdrop-blur border-white/20">
                      <SelectValue placeholder="Any" />
                    </SelectTrigger>
                    <SelectContent className="bg-background/95 backdrop-blur border-white/20">
                      <SelectItem value="1">1 Storey</SelectItem>
                      <SelectItem value="2">2 Storeys</SelectItem>
                      <SelectItem value="3">3+ Storeys</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator className="bg-accent/20" />

                {/* Parking Types */}
                <div className="space-y-4">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Car className="h-4 w-4" />
                    Parking Type(s)
                  </Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {parkingOptions.map((parking) => (
                      <div key={parking} className="flex items-center space-x-2">
                        <Checkbox
                          id={parking}
                          checked={parkingTypes.includes(parking)}
                          onCheckedChange={(checked) => handleParkingChange(parking, checked as boolean)}
                          className="border-white/20"
                        />
                        <Label htmlFor={parking} className="text-sm">
                          {parking}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator className="bg-accent/20" />

                {/* Extra Features */}
                <div className="space-y-4">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    Extra Features & Filters
                  </Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {extraFeaturesOptions.map((feature) => (
                      <div key={feature} className="flex items-center space-x-2">
                        <Checkbox
                          id={feature}
                          checked={extraFeatures.includes(feature)}
                          onCheckedChange={(checked) => handleFeatureChange(feature, checked as boolean)}
                          className="border-white/20"
                        />
                        <Label htmlFor={feature} className="text-sm">
                          {feature}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Search Actions */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6">
              <Button size="lg" className="flex-1 h-14 text-lg bg-gradient-to-r from-accent to-accent/80 hover:from-accent/90 hover:to-accent/70">
                <Search className="mr-2 h-5 w-5" />
                Search Properties
              </Button>
              <Button variant="outline" size="lg" className="h-14 px-8 bg-background/50 backdrop-blur border-white/20">
                <Filter className="mr-2 h-4 w-4" />
                Save Search
              </Button>
            </div>

            {/* Active Filters */}
            {(parkingTypes.length > 0 || extraFeatures.length > 0) && (
              <div className="space-y-3">
                <Label className="text-sm font-medium">Active Filters</Label>
                <div className="flex flex-wrap gap-2">
                  {[...parkingTypes, ...extraFeatures].map((filter) => (
                    <Badge key={filter} variant="secondary" className="bg-accent/10 text-accent">
                      {filter}
                      <X
                        className="ml-1 h-3 w-3 cursor-pointer"
                        onClick={() => {
                          if (parkingTypes.includes(filter)) {
                            handleParkingChange(filter, false)
                          } else {
                            handleFeatureChange(filter, false)
                          }
                        }}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
