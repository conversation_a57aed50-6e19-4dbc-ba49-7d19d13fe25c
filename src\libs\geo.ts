export const SUPPORTED_COUNTRIES = ['US', 'CA'] as const;
export type SupportedCountry = typeof SUPPORTED_COUNTRIES[number];

export function isLocationSupported(countryCode: string): countryCode is SupportedCountry {
  return SUPPORTED_COUNTRIES.includes(countryCode as SupportedCountry);
}

export function getCountryName(countryCode: string): string {
  const countries: Record<string, string> = {
    US: 'United States',
    CA: 'Canada',
    // Add more countries as needed
  }

  return countries[countryCode] || 'Your Region'
}

export function getRegionSpecificPolicies(countryCode: string) {
  const policies = {
    US: {
      privacyLaws: ['CCPA', 'CPRA', 'VCDPA', 'CPA', 'CTDPA'],
      dataRetention: '24 months',
      dataTransfer: 'Within US and Canada only',
    },
    CA: {
      privacyLaws: ['PIPEDA'],
      dataRetention: '24 months',
      dataTransfer: 'Within US and Canada only',
    },
  };
  return policies[countryCode as SupportedCountry] || null;
} 