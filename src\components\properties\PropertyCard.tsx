import Image from 'next/image';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/lib/utils';
import { Property } from '@/types/property';

interface PropertyCardProps {
  property: Property;
}

export function PropertyCard({ property }: PropertyCardProps) {
  const primaryImage = property.images[0]?.url;

  return (
    <Link href={`/properties/${property.id}`}>
      <Card className="overflow-hidden hover:shadow-lg transition-shadow">
        {primaryImage && (
          <div className="relative aspect-video">
            <Image
              src={primaryImage}
              alt={property.title}
              fill
              className="object-cover"
            />
          </div>
        )}
        <CardHeader>
          <CardTitle className="line-clamp-1">{property.title}</CardTitle>
          <p className="text-2xl font-semibold text-primary">
            {formatCurrency(property.price)}
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
            <span>{property.bedrooms} beds</span>
            <span>•</span>
            <span>{property.bathrooms} baths</span>
            <span>•</span>
            <span>{property.square_footage} sq ft</span>
          </div>
          <p className="text-sm text-muted-foreground line-clamp-2 mb-4">
            {property.description}
          </p>
          <div className="flex items-center gap-2">
            {property.seller.avatar_url && (
              <div className="relative w-6 h-6 rounded-full overflow-hidden">
                <Image
                  src={property.seller.avatar_url}
                  alt={property.seller.full_name}
                  fill
                  className="object-cover"
                />
              </div>
            )}
            <div className="text-sm">
              <p className="font-medium">{property.seller.full_name}</p>
              <p className="text-muted-foreground">{property.seller.role}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
