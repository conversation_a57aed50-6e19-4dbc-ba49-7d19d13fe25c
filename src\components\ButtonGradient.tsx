"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface ButtonGradientProps {
  title?: string;
  onClick?: () => void;
  className?: string;
  children?: React.ReactNode;
}

const ButtonGradient = ({
  title = "Get started",
  onClick = () => { },
  className,
  children,
}: ButtonGradientProps) => {
  return (
    <button
      className={cn(
        "group relative inline-flex animate-rainbow cursor-pointer items-center justify-center rounded-xl border-0 bg-[length:200%] py-2 font-medium transition-colors [background-clip:padding-box,border-box,border-box] [background-origin:border-box] [border:calc(0.08*1rem)_solid_transparent] focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 text-white before:absolute before:bottom-[-20%] before:left-1/2 before:z-0 before:h-1/5 before:w-3/5 before:-translate-x-1/2 before:animate-rainbow before:bg-[linear-gradient(90deg,hsl(var(--color-1)),hsl(var(--color-5)),hsl(var(--color-3)),hsl(var(--color-4)),hsl(var(--color-2)))] before:bg-[length:200%] before:[filter:blur(calc(0.8*1rem))] bg-[linear-gradient(#121213,#121213),linear-gradient(#121213_50%,rgb(18,18,19)_80%,rgb(18,18,19)),linear-gradient(90deg,hsl(var(--color-1)),hsl(var(--color-5)),hsl(var(--color-3)),hsl(var(--color-4)),hsl(var(--color-2)))] dark:bg-[linear-gradient(#ffffff,#ffffff),linear-gradient(#ffffff_20%,rgb(255,255,255)_20%,rgb(255,255,255)),linear-gradient(90deg,hsl(var(--color-1)),hsl(var(--color-5)),hsl(var(--color-3)),hsl(var(--color-4)),hsl(var(--color-2)))] h-8 px-4 text-sm min-w-[100px]",
        className
      )}
      onClick={onClick}
    >
      {children || title}
    </button>
  );
};

export default ButtonGradient;
