import { prisma } from '../prisma';
import { ServiceProvider, ServiceType, Prisma } from '@prisma/client';

export class ServiceProviderService {
  static async createServiceProvider(data: {
    userId: string;
    serviceType: ServiceType;
    businessName: string;
    description?: string;
    licenseNumber?: string;
    regions?: any;
    rates?: any;
  }): Promise<ServiceProvider> {
    return prisma.serviceProvider.create({
      data,
    });
  }

  static async updateServiceProvider(
    id: string,
    data: Partial<ServiceProvider>
  ): Promise<ServiceProvider> {
    return prisma.serviceProvider.update({
      where: { id },
      data,
    });
  }

  static async getServiceProvider(id: string): Promise<ServiceProvider | null> {
    return prisma.serviceProvider.findUnique({
      where: { id },
      include: {
        user: true,
        bookings: {
          include: {
            property: true,
            user: true,
          },
        },
      },
    });
  }

  static async listServiceProviders(params: {
    skip?: number;
    take?: number;
    serviceType?: ServiceType;
    regions?: any;
  }): Promise<ServiceProvider[]> {
    const { skip = 0, take = 10, ...filters } = params;

    return prisma.serviceProvider.findMany({
      skip,
      take,
      where: filters,
      include: {
        user: true,
      },
      orderBy: {
        rating: 'desc',
      },
    });
  }

  static async updateRating(
    id: string,
    rating: number
  ): Promise<ServiceProvider> {
    return prisma.serviceProvider.update({
      where: { id },
      data: {
        rating: new Prisma.Decimal(rating),
      },
    });
  }

  static async getServiceProviderByUserId(
    userId: string
  ): Promise<ServiceProvider | null> {
    return prisma.serviceProvider.findUnique({
      where: { userId },
      include: {
        bookings: {
          include: {
            property: true,
            user: true,
          },
        },
      },
    });
  }
} 