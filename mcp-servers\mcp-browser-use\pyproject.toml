[project]
name = "mcp_server_browser_use"
version = "0.1.8"
description = "MCP server for browser-use"
readme = "README.md"
requires-python = ">=3.11"
authors = [{ name = "<PERSON>" }]
license = { text = "MIT" }
classifiers = [
  "Development Status :: 4 - Beta",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.11",
  "Operating System :: OS Independent",
]

dependencies = [
  "pydantic-settings>=2.0.0",
  "mcp>=1.6.0",
  "typer>=0.12.0",
  "browser-use==0.1.41",
  "pyperclip==1.9.0",
  "json-repair",
  "langchain-mistralai==0.2.4",
  "MainContentExtractor==0.0.4",
  "langchain-ibm==0.3.10",
  "langchain_mcp_adapters==0.0.9",
  "langgraph==0.3.34",
  "langchain-community",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_server_browser_use"]

[project.scripts]
mcp-server-browser-use = "mcp_server_browser_use.server:main"
mcp-browser-cli = "mcp_server_browser_use.cli:app"

[tool.pyright]
include = ["src/mcp_server_browser_use"]
venvPath = "."
venv = ".venv"

[tool.ruff.lint]
select = ["E", "F", "I"]
ignore = []

[tool.ruff]
line-length = 150
target-version = "py311"

[tool.uv]
dev-dependencies = ["pyright>=1.1.378", "pytest>=8.3.3", "ruff>=0.6.9"]
