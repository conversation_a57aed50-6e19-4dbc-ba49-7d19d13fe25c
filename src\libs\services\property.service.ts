import { prisma } from '../prisma';
import { Property, PropertyStatus, PropertyImage, Prisma } from '@prisma/client';

export class PropertyService {
  static async createProperty(data: {
    sellerId: string;
    title: string;
    description?: string;
    price: number;
    bedrooms?: number;
    bathrooms?: number;
    sqft?: number;
    propertyType: string;
    address: any;
    coordinates?: any;
    features?: any;
  }): Promise<Property> {
    return prisma.property.create({
      data: {
        ...data,
        price: new Prisma.Decimal(data.price),
      },
    });
  }

  static async updateProperty(
    id: string,
    data: Partial<Property>
  ): Promise<Property> {
    return prisma.property.update({
      where: { id },
      data,
    });
  }

  static async getProperty(id: string): Promise<Property | null> {
    return prisma.property.findUnique({
      where: { id },
      include: {
        images: true,
        seller: true,
      },
    });
  }

  static async listProperties(params: {
    skip?: number;
    take?: number;
    status?: PropertyStatus;
    minPrice?: number;
    maxPrice?: number;
    propertyType?: string;
    bedrooms?: number;
    bathrooms?: number;
  }): Promise<Property[]> {
    const { skip = 0, take = 10, ...filters } = params;
    
    return prisma.property.findMany({
      skip,
      take,
      where: {
        ...filters,
        status: filters.status || 'active',
      },
      include: {
        images: true,
        seller: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  static async addPropertyImage(data: {
    propertyId: string;
    url: string;
    storagePath: string;
    isPrimary?: boolean;
  }): Promise<PropertyImage> {
    if (data.isPrimary) {
      // Unset any existing primary images
      await prisma.propertyImage.updateMany({
        where: { propertyId: data.propertyId, isPrimary: true },
        data: { isPrimary: false },
      });
    }

    return prisma.propertyImage.create({
      data,
    });
  }

  static async deletePropertyImage(id: string): Promise<void> {
    await prisma.propertyImage.delete({
      where: { id },
    });
  }

  static async updatePropertyStatus(
    id: string,
    status: PropertyStatus
  ): Promise<Property> {
    return prisma.property.update({
      where: { id },
      data: { status },
    });
  }
} 