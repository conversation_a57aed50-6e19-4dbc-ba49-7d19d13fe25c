import { redirect } from 'next/navigation'

const SUPPORTED_COUNTRIES = ['us', 'ca']

interface CountryPathPageProps {
  params: Promise<{
    country: string
    path: string[]
  }>
}

export default async function CountryPathPage({ params }: CountryPathPageProps) {
  const resolvedParams = await params
  const country = resolvedParams.country.toLowerCase()
  const path = resolvedParams.path.join('/')

  // Validate country
  if (!SUPPORTED_COUNTRIES.includes(country)) {
    redirect(`/unsupported-region?country=${country.toUpperCase()}`)
  }

  // Redirect to the path without country prefix
  // This allows the app to work normally while maintaining country context
  redirect(`/${path}`)
}
