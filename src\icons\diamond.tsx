const Diamond = () => {
  return (
    <svg
      width="23"
      height="20"
      viewBox="0 0 23 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.03518 0.5C5.45006 0.5 4.90366 0.792427 4.57909 1.27927L0.98987 6.66311C0.770137 6.99271 0.74386 7.41478 0.921015 7.76909C2.88512 11.6973 5.61467 15.1932 8.94923 18.0514L10.7 19.5521C11.1603 19.9467 11.8397 19.9467 12.3 19.5521L14.0508 18.0514C17.3853 15.1932 20.1149 11.6973 22.079 7.76909C22.2561 7.41478 22.2299 6.99271 22.0101 6.66311L18.4209 1.27927C18.0963 0.792427 17.5499 0.5 16.9648 0.5H6.03518ZM5.82717 2.11132C5.87353 2.04178 5.95159 2 6.03518 2H8.37499L6.45987 6.59629C6.41576 6.70216 6.38246 6.81135 6.36002 6.92224C5.65619 6.86985 4.95296 6.80669 4.25057 6.73276L2.84486 6.58479L5.82717 2.11132ZM2.77865 8.08611C4.57205 11.3761 6.94021 14.3214 9.77321 16.7812L6.64775 8.44667C5.79541 8.38808 4.94387 8.31402 4.09355 8.22452L2.77865 8.08611ZM8.28481 8.54016L11.5 17.114L14.7152 8.54016C12.5728 8.63762 10.4272 8.63762 8.28481 8.54016ZM16.3523 8.44667L13.2268 16.7812C16.0598 14.3214 18.4279 11.3761 20.2213 8.08611L18.9064 8.22452C18.0561 8.31402 17.2046 8.38807 16.3523 8.44667ZM20.1551 6.58479L18.7494 6.73276C18.047 6.80669 17.3438 6.86985 16.64 6.92224C16.6175 6.81135 16.5842 6.70216 16.5401 6.59629L14.625 2H16.9648C17.0484 2 17.1265 2.04178 17.1728 2.11133L20.1551 6.58479ZM15.0917 7.02005C12.6988 7.14432 10.3012 7.14432 7.9083 7.02005L9.99999 2H13L15.0917 7.02005Z"
        fill="#52DE82"
      />
    </svg>
  );
};

export default Diamond;
