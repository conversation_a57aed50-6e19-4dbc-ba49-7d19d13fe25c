#MAKE

MAKE_ORGANIZATION_ID=
MAKE_TEAM_ID=
MAKE_API_KEY=
MAKE_API_URL=

#N8N
N8N_API_KEY=
N8N_API_URL=
N8N_WEBHOOK_URL=

POSTGRES_USER=postgres
POSTGRES_PASSWORD=Shared4w0rk!!!
POSTGRES_DBNAME=postgres
DATABASE_HOST=db.yfznlsisxsnymkvydzha.supabase.co
DATABASE_PORT=5432

#DB PRISMA
DATABASE_URL=*****************************************************************************************************/postgres

NEXT_PUBLIC_MAPBOX_API_KEY=pk.eyJ1IjoiamF2aWFucGljYXJkbzMzIiwiYSI6ImNtYjY4ZGRyazBiaWYybHEyMWpnNGN4cDQifQ.m63aGFvbfzrQhT3sWlSbDQ
# Stripe keys
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51Qfy9dP82YH9JfOlFmwTk0RHvE7l6sow7ImJBI4c1GzcbhFydoVpb3DMevP8B3C4gj95qCoEise8vEhgFgWxhLEN000RfInKNH
STRIPE_SECRET_KEY=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
STRIPE_WEBHOOK_SECRET=

# Clerk keys
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_ZGlzY3JldGUtaGFyZS03My5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_qMWT9IyzEIVMz6bjQ7P4ZBVHsiy9ZERxHbGKipVHcl
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up

# Supabase keys
NEXT_PUBLIC_SUPABASE_URL=https://yfznlsisxsnymkvydzha.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qUBshv6Wi-90ZAEnUM2RXuhR77QFHnmEpI6O-y7l3BE

# Your app's base URL
NEXT_PUBLIC_APP_URL=http://localhost:3000

#Resend
RESEND_API_KEY=re_H7U9GBDe_2D8FiBaiP6A59v5YHuHTXKei

#WordPress
WORDPRESS_DB_USER=user
WORDPRESS_DB_PASSWORD=5vBiXJjkhIC.
WORDPRESS_DB_NAME=exampledb
WP_REST_ENDPOINT=http://*************

#MySQL for WordPress
MYSQL_DATABASE=wp_db
MYSQL_USER=admin
MYSQL_PASSWORD=adminpass
MYSQL_RANDOM_ROOT_PASSWORD='1'
