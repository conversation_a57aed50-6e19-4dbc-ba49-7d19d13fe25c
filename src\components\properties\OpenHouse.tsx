'use client';

import { useState, useEffect } from 'react';
import { AIPropertyService, OpenHouseData, PropertyReport as PropertyReportType } from '@/lib/ai-services';
import { PropertyReport } from './PropertyReport';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Sparkles, Download, Share, Calendar, MapPin } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface OpenHouseProps {
    property: any;
    className?: string;
}

export function OpenHouse({ property, className = '' }: OpenHouseProps) {
    const [openHouseData, setOpenHouseData] = useState<OpenHouseData | null>(null);
    const [loading, setLoading] = useState(false);
    const [activeTab, setActiveTab] = useState('overview');

    const generateOpenHouseData = async () => {
        setLoading(true);
        try {
            const data = await AIPropertyService.generateOpenHouseData(property);
            setOpenHouseData(data);
        } catch (error) {
            console.error('Error generating open house data:', error);
        } finally {
            setLoading(false);
        }
    };

    const reportCategories = {
        lifestyle: ['schools', 'shopping', 'grocery', 'dining', 'entertainment'],
        transportation: ['transit', 'walkability'],
        services: ['healthcare', 'fitness', 'services', 'movein'],
        location: ['landmarks', 'neighborhood']
    };

    const getReportsForCategory = (category: keyof typeof reportCategories) => {
        if (!openHouseData) return [];
        return openHouseData.reports.filter(report =>
            reportCategories[category].includes(report.type)
        );
    };

    const getOverallScore = () => {
        if (!openHouseData) return 0;
        const reportsWithScores = openHouseData.reports.filter(r => r.score);
        if (reportsWithScores.length === 0) return 0;
        const average = reportsWithScores.reduce((sum, r) => sum + (r.score || 0), 0) / reportsWithScores.length;
        return Math.round(average);
    };

    if (!openHouseData && !loading) {
        return (
            <Card className={className}>
                <CardHeader className="text-center">
                    <CardTitle className="flex items-center justify-center gap-2">
                        <Sparkles className="h-6 w-6 text-primary" />
                        AI-Powered Open House
                    </CardTitle>
                    <p className="text-muted-foreground">
                        Generate comprehensive property insights and neighborhood reports with AI
                    </p>
                </CardHeader>
                <CardContent className="text-center">
                    <Button onClick={generateOpenHouseData} size="lg" className="gap-2">
                        <Sparkles className="h-5 w-5" />
                        Generate Open House Data
                    </Button>
                </CardContent>
            </Card>
        );
    }

    if (loading) {
        return (
            <Card className={className}>
                <CardContent className="flex items-center justify-center py-12">
                    <div className="text-center space-y-4">
                        <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                        <div>
                            <h3 className="font-semibold">Generating AI Reports</h3>
                            <p className="text-sm text-muted-foreground">
                                Creating comprehensive neighborhood insights...
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className={`space-y-6 ${className}`}>
            {/* Header Section */}
            <Card>
                <CardHeader>
                    <div className="flex flex-col md:flex-row justify-between items-start gap-4">
                        <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                                <Sparkles className="h-6 w-6 text-primary" />
                                <h2 className="text-2xl font-bold">AI-Generated Open House</h2>
                                <Badge variant="secondary" className="bg-primary/10 text-primary">
                                    Score: {getOverallScore()}/100
                                </Badge>
                            </div>
                            <div className="flex items-center gap-2 text-muted-foreground mb-4">
                                <MapPin className="h-4 w-4" />
                                <span className="text-sm">
                                    {typeof property.address === 'string'
                                        ? property.address
                                        : `${property.address?.street || ''}, ${property.address?.city || ''}`}
                                </span>
                                <span className="text-sm">•</span>
                                <span className="text-sm">
                                    Generated {new Date(openHouseData?.generatedAt || new Date()).toLocaleDateString()}
                                </span>
                            </div>
                            <div className="text-sm text-muted-foreground">
                                Comprehensive property insights powered by AI • {openHouseData?.reports.length} reports generated
                            </div>
                        </div>
                        <div className="flex gap-2">
                            <Button variant="outline" size="sm" className="gap-2">
                                <Share className="h-4 w-4" />
                                Share
                            </Button>
                            <Button variant="outline" size="sm" className="gap-2">
                                <Download className="h-4 w-4" />
                                Download
                            </Button>
                            <Button variant="outline" size="sm" className="gap-2">
                                <Calendar className="h-4 w-4" />
                                Schedule Tour
                            </Button>
                        </div>
                    </div>
                </CardHeader>
            </Card>

            {/* Enhanced Property Description */}
            {openHouseData?.description && (
                <Card>
                    <CardHeader>
                        <CardTitle>AI-Enhanced Property Description</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-sm leading-relaxed">{openHouseData.description}</p>
                        <div className="mt-4 pt-4 border-t">
                            <div className="flex items-center justify-between text-sm text-muted-foreground">
                                <span>✨ Enhanced with AI-powered language optimization</span>
                                <Button variant="ghost" size="sm" onClick={generateOpenHouseData}>
                                    Regenerate
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Tabbed Reports Section */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                <TabsList className="grid w-full grid-cols-5">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="lifestyle">Lifestyle</TabsTrigger>
                    <TabsTrigger value="transportation">Transport</TabsTrigger>
                    <TabsTrigger value="services">Services</TabsTrigger>
                    <TabsTrigger value="location">Location</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {/* Quick Stats */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Property Highlights</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="text-center p-3 border rounded-lg">
                                        <p className="text-2xl font-bold text-primary">{getOverallScore()}</p>
                                        <p className="text-sm text-muted-foreground">Overall Score</p>
                                    </div>
                                    <div className="text-center p-3 border rounded-lg">
                                        <p className="text-2xl font-bold text-primary">{openHouseData?.reports.length}</p>
                                        <p className="text-sm text-muted-foreground">Reports</p>
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Price</span>
                                        <span className="font-medium">{formatCurrency(property.price)}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Type</span>
                                        <span className="font-medium">{property.propertyType}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Size</span>
                                        <span className="font-medium">{property.sqft} sq ft</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Top Scoring Reports */}
                        {openHouseData?.reports
                            .filter(r => r.score && r.score >= 85)
                            .slice(0, 2)
                            .map(report => (
                                <PropertyReport key={report.id} report={report} />
                            ))}
                    </div>
                </TabsContent>

                <TabsContent value="lifestyle" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {getReportsForCategory('lifestyle').map(report => (
                            <PropertyReport key={report.id} report={report} />
                        ))}
                    </div>
                </TabsContent>

                <TabsContent value="transportation" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {getReportsForCategory('transportation').map(report => (
                            <PropertyReport key={report.id} report={report} />
                        ))}
                    </div>
                </TabsContent>

                <TabsContent value="services" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {getReportsForCategory('services').map(report => (
                            <PropertyReport key={report.id} report={report} />
                        ))}
                    </div>
                </TabsContent>

                <TabsContent value="location" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {getReportsForCategory('location').map(report => (
                            <PropertyReport key={report.id} report={report} />
                        ))}
                    </div>
                </TabsContent>
            </Tabs>

            {/* Regenerate Button */}
            <Card>
                <CardContent className="flex items-center justify-between py-4">
                    <div>
                        <h4 className="font-semibold">Need Updated Information?</h4>
                        <p className="text-sm text-muted-foreground">
                            Regenerate all reports with the latest AI insights
                        </p>
                    </div>
                    <Button onClick={generateOpenHouseData} disabled={loading} className="gap-2">
                        <Sparkles className="h-4 w-4" />
                        Regenerate Reports
                    </Button>
                </CardContent>
            </Card>
        </div>
    );
}
