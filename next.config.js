/** @type {import('next').NextConfig} */
const nextConfig = {
	eslint: {
		ignoreDuringBuilds: true,
	},
	reactStrictMode: true,
	images: {
		domains: [
			// NextJS <Image> component needs to whitelist domains for src={}
			'lh3.googleusercontent.com',
			'pbs.twimg.com',
			'images.unsplash.com',
			'logos-world.net',
			'localhost',
			'cdn-icons-png.flaticon.com',
			'res.cloudinary.com',
			'blogger.googleusercontent.com',
			'fast-strapi-cms-651b34b82e95.herokuapp.comhttps',
			'secure.gravatar.com',
			'img.clerk.com',
			'************',
		],
	},
	// Add optimizations from Next.js 15
	experimental: {
		optimizePackageImports: [
			'lucide-react',
			'@radix-ui/react-icons',
			'@heroicons/react',
			'date-fns',
		],
		// Enable React compiler for better performance
		// reactCompiler: true, // Uncomment if you want to try the React compiler
	},
}

module.exports = nextConfig
