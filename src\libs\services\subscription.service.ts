import { prisma } from '../prisma';
import { Subscription } from '@prisma/client';

export class SubscriptionService {
  static async createSubscription(data: {
    userId: string;
    stripeSubscriptionId: string;
    status: string;
    planType: string;
    startsAt: Date;
    endsAt: Date;
  }): Promise<Subscription> {
    return prisma.subscription.create({
      data,
    });
  }

  static async updateSubscription(
    id: string,
    data: Partial<Subscription>
  ): Promise<Subscription> {
    return prisma.subscription.update({
      where: { id },
      data,
    });
  }

  static async getSubscription(id: string): Promise<Subscription | null> {
    return prisma.subscription.findUnique({
      where: { id },
      include: {
        user: true,
      },
    });
  }

  static async getUserActiveSubscription(
    userId: string
  ): Promise<Subscription | null> {
    const now = new Date();
    return prisma.subscription.findFirst({
      where: {
        userId,
        status: 'active',
        startsAt: { lte: now },
        endsAt: { gte: now },
      },
      include: {
        user: true,
      },
    });
  }

  static async listUserSubscriptions(
    userId: string
  ): Promise<Subscription[]> {
    return prisma.subscription.findMany({
      where: { userId },
      include: {
        user: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  static async cancelSubscription(id: string): Promise<Subscription> {
    return prisma.subscription.update({
      where: { id },
      data: {
        status: 'cancelled',
        endsAt: new Date(),
      },
    });
  }

  static async renewSubscription(
    id: string,
    newEndsAt: Date
  ): Promise<Subscription> {
    return prisma.subscription.update({
      where: { id },
      data: {
        status: 'active',
        endsAt: newEndsAt,
      },
    });
  }
} 