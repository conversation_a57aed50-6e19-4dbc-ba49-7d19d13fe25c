'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import config from '@/config';
import { Facebook, Twitter, Linkedin, Mail, Phone, MapPin, Home, Search, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useUserType } from '@/contexts/UserTypeContext';

export function Footer() {
  const pathname = usePathname();
  const { userType } = useUserType();
  const [currentRegion, setCurrentRegion] = useState<string>('CA');

  useEffect(() => {
    // Get region from localStorage or default to CA
    const storedRegion = localStorage.getItem('userCountry');
    if (storedRegion && ['US', 'CA'].includes(storedRegion)) {
      setCurrentRegion(storedRegion);
    }
  }, []);

  // Don't show footer on certain pages
  if (['/login', '/signup', '/reset-password'].includes(pathname)) {
    return null;
  }

  return (
    <>
      {/* Main Footer */}
      <footer className="border-t border-border/40 bg-primary/5">
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 w-full max-w-7xl mx-auto">
            {/* Company Info */}
            <div>
              <h3 className="text-lg font-semibold mb-4">SoNo Brokers</h3>
              <p className="text-muted-foreground mb-4">
                Commission-Free Real Estate Platform. Buy and sell properties directly,
                save on commission fees, and access professional services on-demand.
              </p>
              <div className="flex space-x-4">
                <Link href={config.socialLinks.twitter} target="_blank" rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors">
                  <Twitter size={20} />
                </Link>
                <Link href={config.socialLinks.facebook} target="_blank" rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors">
                  <Facebook size={20} />
                </Link>
                <Link href={config.socialLinks.linkedin} target="_blank" rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors">
                  <Linkedin size={20} />
                </Link>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-semibold mb-4">
                {userType === 'buyer' ? 'For Buyers' : 'For Sellers'}
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link href={`/${currentRegion.toLowerCase()}`} className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                    <Home size={16} />
                    {currentRegion === 'CA' ? 'Canada Home' : 'USA Home'}
                  </Link>
                </li>
                <li>
                  <Link href={`/${currentRegion.toLowerCase()}/properties`} className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                    <Search size={16} />
                    {userType === 'buyer' ? 'Browse Properties' : 'View Listings'}
                  </Link>
                </li>
                {userType === 'seller' && (
                  <li>
                    <Link href={`/${currentRegion.toLowerCase()}/list-property`} className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                      <Users size={16} />
                      List Property
                    </Link>
                  </li>
                )}
                <li>
                  <Link href={`/${currentRegion.toLowerCase()}/services`} className="text-muted-foreground hover:text-primary transition-colors">
                    Professional Services
                  </Link>
                </li>
                <li>
                  <Link href={`/${currentRegion.toLowerCase()}/how-it-works`} className="text-muted-foreground hover:text-primary transition-colors">
                    How It Works
                  </Link>
                </li>
                <li>
                  <Link href="/about" className="text-muted-foreground hover:text-primary transition-colors">
                    About Us
                  </Link>
                </li>
              </ul>
            </div>

            {/* Contact Info & Legal */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Contact & Legal</h3>
              <ul className="space-y-3 mb-6">
                <li className="flex items-center gap-2 text-muted-foreground">
                  <Mail size={18} />
                  <a href={`mailto:${config.contact.email}`} className="hover:text-primary transition-colors">
                    {config.contact.email}
                  </a>
                </li>
                <li className="flex items-center gap-2 text-muted-foreground">
                  <Phone size={18} />
                  <a href={`tel:${config.contact.phone}`} className="hover:text-primary transition-colors">
                    {config.contact.phone}
                  </a>
                </li>
              </ul>

              {/* Legal Links */}
              <div className="space-y-2">
                <Link href="/terms" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                  Terms of Service
                </Link>
                <Link href="/privacy-policy" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                  Privacy Policy
                </Link>
                <Link href="/cookies" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                  Cookie Policy
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* MLS® and REALTOR® Trademark Notice - Separate Section */}
      <div className="bg-muted/20 border-t border-border/40">
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-4xl mx-auto">
            <h4 className="text-sm font-semibold mb-4 text-foreground">MLS® and REALTOR® Trademark Notice</h4>
            <div className="text-xs text-muted-foreground space-y-3 leading-relaxed">
              <p>
                The trademarks MLS®, Multiple Listing Service® and the associated logos are owned by The Canadian Real Estate Association (CREA) and identify the quality of services provided by real estate professionals who are members of CREA. Used under license.
              </p>
              <p>
                The trademarks REALTOR®, REALTORS® and the REALTOR® logo are controlled by The Canadian Real Estate Association (CREA) and identify real estate professionals who are members of CREA.
              </p>
              <p>
                References to MLS® System listings and advertising on SoNo Brokers on this website are made in connection with the services provided by:
              </p>
              <p className="font-medium pl-4">
                • The JAVIAN PICARDO GROUP INC brokerage who is licensed to trade across Alberta, BC, Saskatchewan, Ontario, New Brunswick and Nova Scotia.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Copyright - Separate Section */}
      <div className="bg-background border-t border-border/40">
        <div className="container mx-auto px-4 py-4">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              © 2025 SoNo Brokers. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
