'use client'

import { useState } from 'react'
import { useUser } from '@clerk/nextjs'
import { Search, Star, ArrowRight, Home, Users, MapPin } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { HeroSection } from '@/components/ca/HeroSection'
import { HowItWorks } from '@/components/ca/HowItWorks'
import { FeaturedProperties } from '@/components/ca/FeaturedProperties'

export default function USALandingPage() {
  const { isSignedIn } = useUser()
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearch = () => {
    if (searchQuery.trim()) {
      window.location.href = `/properties?search=${encodeURIComponent(searchQuery)}`
    }
  }

  const handleStartBrowsing = () => {
    window.location.href = '/properties'
  }

  const handleListProperty = () => {
    if (isSignedIn) {
      window.location.href = '/list-property'
    } else {
      window.location.href = '/sign-up'
    }
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Main Hero Content - full width */}
      <div className="text-center mb-16">
        <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">Find Your Perfect<br /><span className="text-orange-500">American Home</span></h1>
        <p className="text-xl md:text-2xl text-gray-600 font-medium mb-12 max-w-2xl mx-auto leading-relaxed">Connect with top-rated realtors nationwide. Discover exceptional properties from coast to coast.</p>
        <div className="flex items-center max-w-2xl mx-auto mb-8 bg-white rounded-full shadow-lg border overflow-hidden">
          <Input type="text" placeholder="Search for properties in New York, California, Texas..." value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} className="flex-1 border-0 focus-visible:ring-0 text-lg px-6 h-8 rounded-none" onKeyPress={(e) => e.key === 'Enter' && handleSearch()} />
          <Button onClick={handleSearch} className="rounded-full bg-orange-500 hover:bg-orange-600 text-white px-6 h-8 m-1" size="sm"><Search className="w-5 h-5" /></Button>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button onClick={handleStartBrowsing} className="bg-orange-500 hover:bg-orange-600 text-white px-8 h-8 rounded-full text-sm font-semibold" size="sm">Start Browsing<ArrowRight className="w-4 h-4 ml-2" /></Button>
          <Button onClick={handleListProperty} variant="outline" className="border-2 border-green-500 text-green-600 hover:bg-green-50 px-8 h-8 rounded-full text-sm font-semibold" size="sm">List Your Property</Button>
        </div>
      </div>
      {/* Statistics Section - full width */}
      <div className="bg-white rounded-2xl shadow-lg border p-8 mb-8 mx-auto max-w-6xl">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
          <div className="space-y-2"><div className="flex items-center justify-center mb-2"><Home className="w-6 h-6 text-orange-500 mr-2" /><div className="text-3xl font-bold text-orange-500">75k+</div></div><div className="text-gray-600 font-medium">Properties</div></div>
          <div className="space-y-2"><div className="flex items-center justify-center mb-2"><Users className="w-6 h-6 text-green-500 mr-2" /><div className="text-3xl font-bold text-green-500">40k+</div></div><div className="text-gray-600 font-medium">Happy Clients</div></div>
          <div className="space-y-2"><div className="flex items-center justify-center mb-2"><MapPin className="w-6 h-6 text-orange-500 mr-2" /><div className="text-3xl font-bold text-orange-500">50+</div></div><div className="text-gray-600 font-medium">States</div></div>
        </div>
      </div>
      <div className="flex justify-center mt-8 mb-8"><Badge variant="outline" className="text-sm px-4 py-2 text-gray-600 font-medium">🇺🇸 United States</Badge></div>
      {/* Add the missing components below the hero section, full width */}
      <HeroSection userType="buyer" isSignedIn={isSignedIn} />
      <HowItWorks userType="buyer" />
      <FeaturedProperties userType="buyer" />
    </div>
  )
}
