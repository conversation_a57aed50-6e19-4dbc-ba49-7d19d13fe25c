'use client';

import React from 'react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
    Calendar, 
    Clock, 
    MapPin, 
    Users, 
    Phone, 
    Mail, 
    Home,
    Bed,
    Bath,
    Square,
    Eye
} from 'lucide-react';

interface OpenHouse {
    id: string;
    propertyId: string;
    property: {
        id: string;
        title: string;
        address: string;
        price: number;
        bedrooms: number;
        bathrooms: number;
        sqft?: number;
        propertyType: string;
        coordinates: {
            lat: number;
            lng: number;
        };
        images?: { url: string; alt?: string }[];
    };
    date: string;
    startTime: string;
    endTime: string;
    description?: string;
    registeredCount: number;
    maxAttendees: number;
    hostName: string;
    hostPhone?: string;
    hostEmail?: string;
}

interface OpenHouseCardProps {
    openHouse: OpenHouse;
    onRegister: (openHouseId: string) => void;
    onViewProperty: (propertyId: string) => void;
    className?: string;
}

export function OpenHouseCard({ 
    openHouse, 
    onRegister, 
    onViewProperty, 
    className = '' 
}: OpenHouseCardProps) {
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatTime = (timeString: string) => {
        return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    };

    const getDaysUntil = (dateString: string) => {
        const today = new Date();
        const openHouseDate = new Date(dateString);
        const diffTime = openHouseDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 0) return 'Today';
        if (diffDays === 1) return 'Tomorrow';
        if (diffDays > 0) return `In ${diffDays} days`;
        return 'Past';
    };

    const isUpcoming = () => {
        const today = new Date();
        const openHouseDate = new Date(openHouse.date);
        return openHouseDate >= today;
    };

    const getAvailabilityColor = () => {
        const percentage = (openHouse.registeredCount / openHouse.maxAttendees) * 100;
        if (percentage >= 90) return 'text-red-600';
        if (percentage >= 70) return 'text-orange-600';
        return 'text-green-600';
    };

    return (
        <Card className={`hover:shadow-lg transition-shadow duration-200 ${className}`}>
            <CardContent className="p-0">
                <div className="flex flex-col md:flex-row">
                    {/* Property Image */}
                    <div className="relative w-full md:w-48 h-48 md:h-auto">
                        <Image
                            src={openHouse.property.images?.[0]?.url || '/api/placeholder/400/300'}
                            alt={openHouse.property.images?.[0]?.alt || openHouse.property.title}
                            fill
                            className="object-cover rounded-t-lg md:rounded-l-lg md:rounded-t-none"
                        />
                        
                        {/* Property Type Badge */}
                        <Badge 
                            variant="secondary" 
                            className="absolute top-2 left-2 bg-white/90 text-gray-800"
                        >
                            {openHouse.property.propertyType}
                        </Badge>

                        {/* Days Until Badge */}
                        <Badge 
                            variant={isUpcoming() ? "default" : "secondary"}
                            className={`absolute top-2 right-2 ${
                                isUpcoming() ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-500'
                            }`}
                        >
                            {getDaysUntil(openHouse.date)}
                        </Badge>
                    </div>

                    {/* Content */}
                    <div className="flex-1 p-4">
                        <div className="flex flex-col h-full">
                            {/* Property Info */}
                            <div className="mb-3">
                                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                                    {openHouse.property.title}
                                </h3>
                                <div className="flex items-center text-gray-600 text-sm mb-2">
                                    <MapPin className="w-4 h-4 mr-1" />
                                    {openHouse.property.address}
                                </div>
                                <div className="text-2xl font-bold text-green-600">
                                    ${openHouse.property.price.toLocaleString()}
                                </div>
                            </div>

                            {/* Property Details */}
                            <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                                <div className="flex items-center">
                                    <Bed className="w-4 h-4 mr-1" />
                                    {openHouse.property.bedrooms} bed
                                </div>
                                <div className="flex items-center">
                                    <Bath className="w-4 h-4 mr-1" />
                                    {openHouse.property.bathrooms} bath
                                </div>
                                {openHouse.property.sqft && (
                                    <div className="flex items-center">
                                        <Square className="w-4 h-4 mr-1" />
                                        {openHouse.property.sqft.toLocaleString()} sqft
                                    </div>
                                )}
                            </div>

                            {/* Open House Details */}
                            <div className="bg-green-50 rounded-lg p-3 mb-3">
                                <div className="flex items-center justify-between mb-2">
                                    <h4 className="font-semibold text-green-800 flex items-center">
                                        <Calendar className="w-4 h-4 mr-1" />
                                        Open House
                                    </h4>
                                    <div className={`text-sm font-medium ${getAvailabilityColor()}`}>
                                        {openHouse.registeredCount}/{openHouse.maxAttendees} registered
                                    </div>
                                </div>
                                
                                <div className="text-sm text-green-700 mb-1">
                                    {formatDate(openHouse.date)}
                                </div>
                                <div className="flex items-center text-sm text-green-700">
                                    <Clock className="w-4 h-4 mr-1" />
                                    {formatTime(openHouse.startTime)} - {formatTime(openHouse.endTime)}
                                </div>

                                {openHouse.description && (
                                    <p className="text-sm text-green-600 mt-2">
                                        {openHouse.description}
                                    </p>
                                )}
                            </div>

                            {/* Host Information */}
                            <div className="mb-4">
                                <div className="text-sm text-gray-600">
                                    <span className="font-medium">Host:</span> {openHouse.hostName}
                                </div>
                                <div className="flex items-center gap-3 mt-1">
                                    {openHouse.hostPhone && (
                                        <div className="flex items-center text-xs text-gray-500">
                                            <Phone className="w-3 h-3 mr-1" />
                                            {openHouse.hostPhone}
                                        </div>
                                    )}
                                    {openHouse.hostEmail && (
                                        <div className="flex items-center text-xs text-gray-500">
                                            <Mail className="w-3 h-3 mr-1" />
                                            {openHouse.hostEmail}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex gap-2 mt-auto">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => onViewProperty(openHouse.property.id)}
                                    className="flex-1"
                                >
                                    <Eye className="w-4 h-4 mr-1" />
                                    View Property
                                </Button>
                                <Button
                                    size="sm"
                                    onClick={() => onRegister(openHouse.id)}
                                    disabled={!isUpcoming() || openHouse.registeredCount >= openHouse.maxAttendees}
                                    className="flex-1 bg-green-600 hover:bg-green-700"
                                >
                                    <Users className="w-4 h-4 mr-1" />
                                    {openHouse.registeredCount >= openHouse.maxAttendees ? 'Full' : 'Register'}
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
