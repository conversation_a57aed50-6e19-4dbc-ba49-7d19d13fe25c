{"name": "micro-sass-fast", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "npx prisma generate && next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "migrate-db": "npx prisma migrate dev --name init"}, "dependencies": {"@azure/storage-blob": "^12.27.0", "@clerk/nextjs": "^5.7.5", "@googlemaps/js-api-loader": "^1.16.8", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.0.1", "@mdx-js/loader": "^2.3.0", "@mdx-js/react": "^2.3.0", "@next/mdx": "^13.5.11", "@next/third-parties": "^15.1.8", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.41", "@remixicon/react": "^4.6.0", "@shadcn/ui": "^0.0.4", "@strapi/blocks-react-renderer": "^1.0.2", "@stripe/stripe-js": "^4.10.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.8", "@types/google.maps": "^3.58.1", "@types/mapbox-gl": "^3.4.1", "@types/wpapi": "^1.1.4", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "form-data": "^4.0.2", "formik": "^2.4.6", "framer-motion": "^12.15.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "lucide-react": "^0.408.0", "mapbox-gl": "^3.12.0", "motion": "^12.14.0", "next": "^15.3.2", "next-plausible": "^3.12.4", "next-sitemap": "^4.2.3", "next-themes": "^0.4.6", "nextjs-toploader": "^1.6.12", "pg": "^8.16.0", "playwright": "^1.52.0", "prop-types": "^15.8.1", "radix-ui": "^1.4.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-email": "^4.0.15", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-responsive-masonry": "^2.7.1", "react-split": "^2.0.14", "react-syntax-highlighter": "^5.8.0", "react-to-print": "^2.15.1", "react-tooltip": "^5.28.1", "resend": "^4.5.1", "sass": "^1.89.0", "sharp": "^0.34.2", "sonner": "^2.0.3", "stripe": "^16.12.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "wpapi": "^1.2.2", "yup": "^1.6.1", "zod": "^3.25.27", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.0", "@types/jest": "^29.5.14", "@types/mdx": "^2.0.13", "@types/node": "^22.15.21", "@types/pdfkit": "^0.13.9", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.21", "daisyui": "^4.12.24", "eslint": "^8.56.0", "eslint-config-next": "^15.1.8", "husky": "^9.1.7", "postcss": "^8.5.3", "prisma": "^6.8.2", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3"}}