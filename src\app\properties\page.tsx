import PropertySearchClient from '@/components/properties/PropertySearchClient';

export default async function PropertiesPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const resolvedSearchParams = await searchParams;

  const queryParams = new URLSearchParams();

  if (resolvedSearchParams.property_type) {
    queryParams.set('propertyType', resolvedSearchParams.property_type as string);
  }
  if (resolvedSearchParams.min_price) {
    queryParams.set('minPrice', resolvedSearchParams.min_price as string);
  }
  if (resolvedSearchParams.max_price) {
    queryParams.set('maxPrice', resolvedSearchParams.max_price as string);
  }
  if (resolvedSearchParams.min_bedrooms) {
    queryParams.set('minBedrooms', resolvedSearchParams.min_bedrooms as string);
  }
  if (resolvedSearchParams.min_bathrooms) {
    queryParams.set('minBathrooms', resolvedSearchParams.min_bathrooms as string);
  }
  if (resolvedSearchParams.min_sqft) {
    queryParams.set('minSqft', resolvedSearchParams.min_sqft as string);
  }
  if (resolvedSearchParams.city) {
    queryParams.set('location', resolvedSearchParams.city as string);
  }
  if (resolvedSearchParams.state) {
    const currentLocation = queryParams.get('location');
    queryParams.set('location', `${currentLocation ? currentLocation + ', ' : ''}${resolvedSearchParams.state}`);
  }
  if (resolvedSearchParams.sort) {
    queryParams.set('sort', resolvedSearchParams.sort as string);
  }
  if (resolvedSearchParams.northEastLat) {
    queryParams.set('northEastLat', resolvedSearchParams.northEastLat as string);
  }
  if (resolvedSearchParams.northEastLng) {
    queryParams.set('northEastLng', resolvedSearchParams.northEastLng as string);
  }
  if (resolvedSearchParams.southWestLat) {
    queryParams.set('southWestLat', resolvedSearchParams.southWestLat as string);
  }
  if (resolvedSearchParams.southWestLng) {
    queryParams.set('southWestLng', resolvedSearchParams.southWestLng as string);
  }

  // For server-side rendering, we'll start with empty properties
  // The client will fetch the data on mount
  const initialProperties: any[] = [];

  return (
    <PropertySearchClient initialProperties={initialProperties} />
  );
}
