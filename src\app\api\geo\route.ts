import { NextRequest, NextResponse } from 'next/server';

/**
 * Region Detection API Documentation
 *
 * This endpoint returns geolocation info based on the user's IP address.
 *
 * ## Testing RegionCheck and Unrecognized Region
 *
 * - By default, RegionCheck fetches `/api/geo` to detect the user's country.
 * - To simulate different regions for testing:
 *   1. **Custom IP via Query Param:**
 *      - Use `/api/geo?ip=*********` (UK IP, not in SUPPORTED_REGIONS) to simulate an unsupported region.
 *      - Use `/api/geo?ip=*********` (Canada), `/api/geo?ip=*******` (USA), etc.
 *   2. **TEST_GEO_IP Environment Variable:**
 *      - Set `TEST_GEO_IP=*********` in your `.env` file to override the IP for all requests (dev only).
 *      - Restart your dev server after changing the env variable.
 *   3. **Clear Cached Country:**
 *      - In your browser dev tools, run: `localStorage.removeItem('userCountry');` and reload the page to force a fresh region check.
 *   4. **Temporarily Change RegionCheck Fetch:**
 *      - In `RegionCheck.tsx`, change the fetch to `/api/geo?ip=*********` for local testing.
 *
 * ## Unrecognized Region Handling
 * - If the detected country is not in SUPPORTED_REGIONS (`US`, `CA`), the user is redirected to `/unsupported-region?country=XX`.
 *
 * This makes it easy to test onboarding and region-specific flows in development.
 */

export async function GET(request: NextRequest) {
  try {
    // Allow IP override via query param for testing
    const { searchParams } = new URL(request.url);
    let ip = searchParams.get('ip');

    // If not provided, try env variable for test IP
    if (!ip && process.env.TEST_GEO_IP) {
      ip = process.env.TEST_GEO_IP;
    }

    // If still not provided, try x-forwarded-for header
    if (!ip) {
      const headersList = request.headers;
      ip = headersList.get('x-forwarded-for') || '127.0.0.1';
    }

    // Use a more reliable geolocation service
    const response = await fetch(`https://ipapi.co/${ip}/json/`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch location data');
    }

    const data = await response.json();
    
    return NextResponse.json({
      country: data.country_code,
      countryName: data.country_name,
      city: data.city,
      region: data.region,
      timezone: data.timezone
    });
  } catch (error) {
    console.error('Error fetching location:', error);
    // Return a default response if the geolocation service fails
    // For testing purposes, return a fixed country
    return NextResponse.json({
      country: 'US',
      countryName: 'United States',
      city: 'Unknown',
      region: 'Unknown',
      timezone: 'UTC'
    });
  }
} 
