fail_fast: true

repos:
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [yaml, json5]

  # - repo: https://github.com/astral-sh/ruff-pre-commit
  #   rev: v0.8.1
  #   hooks:
  #     - id: ruff-format
  #     - id: ruff
  #       args: [--fix, --exit-non-zero-on-fix]

  - repo: local
    hooks:
      - id: uv-lock-check
        name: Check uv.lock is up to date
        entry: uv lock --check
        language: system
        files: ^(pyproject\.toml|uv\.lock)$
        pass_filenames: false
