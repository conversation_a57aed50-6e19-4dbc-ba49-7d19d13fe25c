import { prisma } from '../prisma';
import { Conversation, Message } from '@prisma/client';

export class MessagingService {
  static async createConversation(data: {
    propertyId?: string;
    participants: string[];
  }): Promise<Conversation> {
    return prisma.conversation.create({
      data: {
        ...data,
        participants: data.participants,
      },
    });
  }

  static async getConversation(id: string): Promise<Conversation | null> {
    return prisma.conversation.findUnique({
      where: { id },
      include: {
        property: true,
        messages: {
          include: {
            sender: true,
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });
  }

  static async getUserConversations(
    userId: string
  ): Promise<Conversation[]> {
    return prisma.conversation.findMany({
      where: {
        participants: {
          array_contains: [userId],
        },
      },
      include: {
        property: true,
        messages: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
          include: {
            sender: true,
          },
        },
      },
      orderBy: {
        lastMessageAt: 'desc',
      },
    });
  }

  static async sendMessage(data: {
    conversationId: string;
    senderId: string;
    message: string;
  }): Promise<Message> {
    const [message, conversation] = await prisma.$transaction([
      prisma.message.create({
        data,
        include: {
          sender: true,
        },
      }),
      prisma.conversation.update({
        where: { id: data.conversationId },
        data: {
          lastMessageAt: new Date(),
        },
      }),
    ]);

    return message;
  }

  static async markMessagesAsRead(
    conversationId: string,
    userId: string
  ): Promise<void> {
    await prisma.message.updateMany({
      where: {
        conversationId,
        senderId: { not: userId },
        read: false,
      },
      data: {
        read: true,
      },
    });
  }

  static async getUnreadMessageCount(
    conversationId: string,
    userId: string
  ): Promise<number> {
    return prisma.message.count({
      where: {
        conversationId,
        senderId: { not: userId },
        read: false,
      },
    });
  }

  static async deleteConversation(id: string): Promise<void> {
    await prisma.$transaction([
      prisma.message.deleteMany({
        where: { conversationId: id },
      }),
      prisma.conversation.delete({
        where: { id },
      }),
    ]);
  }
} 