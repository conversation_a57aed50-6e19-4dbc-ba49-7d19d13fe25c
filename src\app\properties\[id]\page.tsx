import { notFound } from 'next/navigation';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import Image from 'next/image';
import { PropertyImageUpload } from '@/components/properties/PropertyImageUpload';
import { OpenHouse } from '@/components/properties/OpenHouse';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatCurrency } from '@/lib/utils';
import { MessageSquare } from 'lucide-react';
import { ContactSellerButton } from '@/components/properties/ContactSellerButton';

interface PropertyPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function PropertyPage({ params }: PropertyPageProps) {
  const resolvedParams = await params;
  const supabase = createServerComponentClient({ cookies });

  const { data: property } = await supabase
    .from('properties')
    .select('*, seller:profiles(*)')
    .eq('id', resolvedParams.id)
    .single();

  if (!property) {
    notFound();
  }

  const {
    data: { user },
  } = await supabase.auth.getUser();

  const isOwner = user?.id === property.seller_id;

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Property Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">{property.title}</h1>
          <p className="text-2xl font-semibold text-primary mt-2">
            {formatCurrency(property.price)}
          </p>
        </div>
        {isOwner && (
          <Button variant="outline" asChild>
            <a href={`/properties/${property.id}/edit`}>Edit Property</a>
          </Button>
        )}
      </div>

      {/* Property Images */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {property.images?.[0] && (
          <div className="relative aspect-video rounded-lg overflow-hidden">
            <Image
              src={property.images[0].url}
              alt={property.title}
              fill
              className="object-cover"
              priority
            />
          </div>
        )}
        <div className="grid grid-cols-2 gap-4">
          {property.images?.slice(1, 5).map((image: { url: string; storagePath: string }, index: number) => (
            <div key={image.storagePath} className="relative aspect-square rounded-lg overflow-hidden">
              <Image
                src={image.url}
                alt={`${property.title} - Image ${index + 2}`}
                fill
                className="object-cover"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="details" className="space-y-6">
        <TabsList className={`grid w-full ${isOwner ? 'grid-cols-3' : 'grid-cols-2'}`}>
          <TabsTrigger value="details">Property Details</TabsTrigger>
          <TabsTrigger value="openhouse">AI Open House</TabsTrigger>
          {isOwner && <TabsTrigger value="manage">Manage</TabsTrigger>}
        </TabsList>

        <TabsContent value="details" className="space-y-8">
          {/* Property Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:col-span-2 space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle>Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="whitespace-pre-wrap">{property.description}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {property.features?.map((feature: string) => (
                      <Badge key={feature} variant="secondary">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle>Property Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Type</span>
                    <span className="font-medium">{property.property_type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Bedrooms</span>
                    <span className="font-medium">{property.bedrooms}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Bathrooms</span>
                    <span className="font-medium">{property.bathrooms}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Square Footage</span>
                    <span className="font-medium">{property.square_footage} sq ft</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Location</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="font-medium">{property.address}</p>
                  <p className="text-muted-foreground">
                    {property.city}, {property.state} {property.zip_code}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Seller Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4">
                    {property.seller.avatar_url && (
                      <div className="relative w-12 h-12 rounded-full overflow-hidden">
                        <Image
                          src={property.seller.avatar_url}
                          alt={property.seller.full_name}
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                    <div>
                      <p className="font-medium">{property.seller.full_name}</p>
                      <p className="text-sm text-muted-foreground">
                        {property.seller.role}
                      </p>
                    </div>
                  </div>
                  {!isOwner && (
                    <ContactSellerButton sellerId={property.seller_id} propertyId={property.id} />
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="openhouse">
          <OpenHouse property={property} />
        </TabsContent>

        {isOwner && (
          <TabsContent value="manage" className="space-y-8">
            {/* Image Upload Section (Only visible to owner) */}
            <Card>
              <CardHeader>
                <CardTitle>Manage Images</CardTitle>
              </CardHeader>
              <CardContent>
                <PropertyImageUpload
                  propertyId={property.id}
                  propertyName={property.title}
                  onUploadComplete={(images) => {
                    // This will be handled client-side
                    console.log('Images updated:', images);
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
