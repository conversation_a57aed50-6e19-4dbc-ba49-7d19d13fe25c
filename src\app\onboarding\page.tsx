"use client";
import { useRouter } from "next/navigation";
import { useState, useTransition } from "react";
import { Building2, Home } from "lucide-react";
import { updateUserRole } from "./actions";

export default function OnboardingPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  const handleRoleSelect = async (role: "seller" | "buyer") => {
    setLoading(true);
    setError(null);
    try {
      await updateUserRole(role);
      startTransition(() => {
        router.replace("/dashboard");
      });
    } catch (err) {
      setError("Failed to set your role. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-gray-800">
      <div className="p-10 rounded-2xl shadow-2xl bg-white/10 backdrop-blur-xl border border-white/20 flex flex-col items-center">
        <h1 className="text-4xl font-extrabold text-white mb-8 tracking-tight">Welcome to SoNoBrokers</h1>
        <p className="text-lg text-white/80 mb-8">How do you want to use the platform?</p>
        <div className="flex gap-8">
          <button
            className="flex flex-col items-center px-8 py-6 rounded-xl bg-white/10 hover:bg-primary/80 text-white text-xl font-semibold shadow-lg transition-all duration-200 border border-white/20 backdrop-blur focus:outline-none focus:ring-2 focus:ring-primary"
            onClick={() => handleRoleSelect("seller")}
            disabled={loading || isPending}
          >
            <Building2 className="h-10 w-10 mb-2" />
            <span>Seller (Default)</span>
          </button>
          <button
            className="flex flex-col items-center px-8 py-6 rounded-xl bg-white/10 hover:bg-primary/80 text-white text-xl font-semibold shadow-lg transition-all duration-200 border border-white/20 backdrop-blur focus:outline-none focus:ring-2 focus:ring-primary"
            onClick={() => handleRoleSelect("buyer")}
            disabled={loading || isPending}
          >
            <Home className="h-10 w-10 mb-2" />
            <span>Buyer</span>
          </button>
        </div>
        {(loading || isPending) && <div className="mt-8 text-white animate-pulse">Setting up your experience...</div>}
        {error && <div className="mt-8 text-red-400 text-lg">{error}</div>}
      </div>
    </div>
  );
} 