const Typescript2 = () => {
  return (
    <svg
      width="27"
      height="27"
      viewBox="0 0 27 27"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_206_3026)">
        <path
          d="M1.45289 0.461914H25.9211C26.469 0.461914 26.9131 0.906025 26.9131 1.45387V25.9221C26.9131 26.47 26.469 26.9141 25.9211 26.9141H1.45289C0.905049 26.9141 0.460938 26.47 0.460938 25.9221V1.45387C0.460938 0.906025 0.905049 0.461914 1.45289 0.461914Z"
          fill="white"
        />
        <path
          d="M1.45289 0.461914H25.9211C26.469 0.461914 26.9131 0.906025 26.9131 1.45387V25.9221C26.9131 26.47 26.469 26.9141 25.9211 26.9141H1.45289C0.905049 26.9141 0.460938 26.47 0.460938 25.9221V1.45387C0.460938 0.906025 0.905049 0.461914 1.45289 0.461914ZM15.1653 14.5345V12.365H5.75116V14.5345H9.11221V24.1942H11.7877V14.5345H15.1653ZM16.232 23.9391C16.6635 24.1591 17.1738 24.3241 17.763 24.4341C18.3521 24.5441 18.973 24.5991 19.6257 24.5991C20.2619 24.5991 20.8662 24.5386 21.4387 24.4176C22.0113 24.2966 22.5133 24.0972 22.9448 23.8195C23.3762 23.5417 23.7178 23.1788 23.9695 22.7306C24.2212 22.2823 24.347 21.7282 24.347 21.0682C24.347 20.5898 24.2751 20.1705 24.1313 19.8102C23.9875 19.45 23.78 19.1296 23.509 18.8491C23.238 18.5686 22.9129 18.317 22.534 18.0943C22.1551 17.8716 21.7278 17.6612 21.252 17.4632C20.9036 17.3202 20.591 17.1813 20.3144 17.0466C20.0379 16.9119 19.8027 16.7743 19.6091 16.6341C19.4155 16.4938 19.2662 16.3454 19.1611 16.1886C19.056 16.032 19.0034 15.8545 19.0034 15.6565C19.0034 15.475 19.0505 15.3114 19.1445 15.1657C19.2385 15.02 19.3713 14.8948 19.5428 14.7903C19.7142 14.6858 19.9244 14.6048 20.1734 14.547C20.4223 14.4893 20.6989 14.4604 21.0032 14.4604C21.2243 14.4604 21.4581 14.4769 21.7043 14.5098C21.9505 14.5429 22.198 14.5938 22.4468 14.6624C22.6958 14.7312 22.9378 14.8178 23.173 14.9224C23.4081 15.0269 23.6252 15.1478 23.8243 15.2853V12.8187C23.4205 12.6647 22.9793 12.5505 22.5009 12.4764C22.0223 12.4021 21.4733 12.365 20.8538 12.365C20.2231 12.365 19.6257 12.4323 19.0615 12.5671C18.4972 12.7018 18.0008 12.9122 17.5721 13.1982C17.1434 13.4842 16.8046 13.8485 16.5556 14.2912C16.3067 14.734 16.1823 15.2633 16.1823 15.8792C16.1823 16.6658 16.4105 17.3368 16.8669 17.8922C17.3232 18.4476 18.016 18.9179 18.9454 19.3029C19.3104 19.4514 19.6507 19.5971 19.9659 19.7401C20.2812 19.8831 20.5537 20.0316 20.7832 20.1856C21.0128 20.3396 21.1939 20.5073 21.3267 20.6888C21.4595 20.8703 21.5259 21.0765 21.5259 21.3075C21.5259 21.478 21.4844 21.6361 21.4014 21.7818C21.3185 21.9276 21.1926 22.0541 21.0239 22.1613C20.8551 22.2686 20.6449 22.3525 20.3932 22.4129C20.1415 22.4735 19.847 22.5037 19.5095 22.5037C18.9343 22.5037 18.3646 22.4034 17.8003 22.2026C17.2361 22.0019 16.7133 21.7008 16.232 21.2993V23.9391Z"
          fill="#3178C6"
        />
      </g>
      <defs>
        <clipPath id="clip0_206_3026">
          <rect
            width="26.4521"
            height="26.4521"
            fill="white"
            transform="translate(0.460938 0.461914)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Typescript2;
