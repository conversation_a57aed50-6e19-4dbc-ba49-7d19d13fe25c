'use client';

import React, { useState, useEffect } from 'react';
import { Calendar, Clock, MapPin, Users, QrCode, Plus, Edit, Eye, Home, Star, School, ShoppingCart, Utensils, Bus, Heart, Dumbbell, Film, Landmark, Shield, Truck, TreePine, TrendingUp, User, Save, Phone, Mail, Building2, Globe } from 'lucide-react';

export default function DashboardPage() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [properties, setProperties] = useState([]);
  const [openHouses, setOpenHouses] = useState([]);
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [userProfile, setUserProfile] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    company: '',
    licenseNumber: '',
    address: '',
    city: '',
    province: '',
    postalCode: '',
    website: '',
    bio: '',
    specializations: [],
    languages: [],
    experience: '',
    profileImage: ''
  });
  const [isProfileSaving, setIsProfileSaving] = useState(false);

  // Mock data
  useEffect(() => {
    setProperties([
      {
        id: 1,
        title: "Modern Downtown Condo",
        address: "123 Main St, Downtown",
        price: "$450,000",
        bedrooms: 2,
        bathrooms: 2,
        sqft: 1200,
        description: "AI-Generated: Stunning modern condo featuring floor-to-ceiling windows, premium finishes, and breathtaking city views. Open-concept living with chef's kitchen, quartz countertops, and stainless steel appliances. Master suite with walk-in closet and spa-inspired bathroom.",
        aiReports: {
          schoolsScore: 9.2,
          walkabilityScore: 95,
          transitScore: 88
        }
      }
    ]);

    setOpenHouses([
      {
        id: 1,
        propertyId: 1,
        date: "2025-06-01",
        startTime: "14:00",
        endTime: "16:00",
        registeredBuyers: 12,
        qrCode: "QR123456"
      }
    ]);
  }, []);

  const PropertyCard = ({ property }) => (
    <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-xl font-bold text-gray-900">{property.title}</h3>
          <p className="text-gray-600 flex items-center mt-1">
            <MapPin className="w-4 h-4 mr-1" />
            {property.address}
          </p>
        </div>
        <div className="text-2xl font-bold text-blue-600">{property.price}</div>
      </div>

      <div className="flex gap-4 mb-4 text-sm text-gray-600">
        <span>{property.bedrooms} beds</span>
        <span>{property.bathrooms} baths</span>
        <span>{property.sqft} sqft</span>
      </div>

      <div className="mb-4">
        <h4 className="font-semibold mb-2">AI-Generated Description:</h4>
        <p className="text-sm text-gray-700 line-clamp-3">{property.description}</p>
      </div>

      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <School className="w-4 h-4 text-blue-500" />
          </div>
          <div className="text-sm font-semibold">{property.aiReports.schoolsScore}/10</div>
          <div className="text-xs text-gray-500">Schools</div>
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <TrendingUp className="w-4 h-4 text-green-500" />
          </div>
          <div className="text-sm font-semibold">{property.aiReports.walkabilityScore}</div>
          <div className="text-xs text-gray-500">Walkability</div>
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <Bus className="w-4 h-4 text-purple-500" />
          </div>
          <div className="text-sm font-semibold">{property.aiReports.transitScore}</div>
          <div className="text-xs text-gray-500">Transit</div>
        </div>
      </div>

      <div className="flex gap-2">
        <button
          onClick={() => { setSelectedProperty(property); setActiveTab('ai-reports'); }}
          className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
        >
          View AI Reports
        </button>
        <button
          onClick={() => { setSelectedProperty(property); setActiveTab('open-house'); }}
          className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm"
        >
          Schedule Open House
        </button>
      </div>
    </div>
  );

  const AIReportsSection = () => {
    const reportTypes = [
      { name: "Nearby Schools Report", icon: School, description: "Top-rated schools to attract families", score: "9.2/10" },
      { name: "Shopping & Retail Report", icon: ShoppingCart, description: "Local malls and retail destinations", score: "8.5/10" },
      { name: "Grocery Stores Report", icon: ShoppingCart, description: "Nearby supermarkets and specialty stores", score: "9.0/10" },
      { name: "Top Food & Dining Report", icon: Utensils, description: "Area's top restaurants and cafés", score: "8.8/10" },
      { name: "Transit & Commuter Report", icon: Bus, description: "Public transport and commute times", score: "8.8/10" },
      { name: "Healthcare & Wellness Report", icon: Heart, description: "Nearby clinics and wellness services", score: "9.1/10" },
      { name: "Fitness & Sports Report", icon: Dumbbell, description: "Top gyms and fitness centers", score: "8.3/10" },
      { name: "Entertainment & Activities Report", icon: Film, description: "Best local entertainment options", score: "8.7/10" },
      { name: "Local Landmarks Report", icon: Landmark, description: "Nearby historical sites and attractions", score: "7.9/10" },
      { name: "Public Services Report", icon: Shield, description: "City services, police, fire departments", score: "9.3/10" },
      { name: "Move-In Services Report", icon: Truck, description: "Nearby movers and storage facilities", score: "8.1/10" },
      { name: "Neighborhood Highlights Report", icon: TreePine, description: "Parks, community features, and charm", score: "8.9/10" },
      { name: "Walkability Score Report", icon: TrendingUp, description: "Pedestrian-friendly rating", score: "95/100" },
      { name: "Lifestyle Snapshot Report", icon: User, description: "Local pace, vibe, and demographics", score: "8.6/10" }
    ];

    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-xl">
          <h2 className="text-2xl font-bold mb-2">AI-Powered Property Reports</h2>
          <p className="opacity-90">Comprehensive neighborhood analysis generated by AI to help buyers make informed decisions</p>
        </div>

        <div className="grid gap-4">
          {reportTypes.map((report, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <report.icon className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{report.name}</h3>
                    <p className="text-sm text-gray-600">{report.description}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-lg font-bold text-green-600">{report.score}</span>
                  <button className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors">
                    Generate Report
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const OpenHouseSection = () => {
    const [showCreateForm, setShowCreateForm] = useState(false);
    const [newOpenHouse, setNewOpenHouse] = useState({
      date: '',
      startTime: '',
      endTime: '',
      maxAttendees: 20
    });

    const handleCreateOpenHouse = () => {
      const openHouse = {
        id: Date.now(),
        propertyId: selectedProperty?.id,
        ...newOpenHouse,
        registeredBuyers: 0,
        qrCode: `QR${Date.now()}`
      };
      setOpenHouses([...openHouses, openHouse]);
      setShowCreateForm(false);
      setNewOpenHouse({ date: '', startTime: '', endTime: '', maxAttendees: 20 });
    };

    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-green-600 to-teal-600 text-white p-6 rounded-xl">
          <h2 className="text-2xl font-bold mb-2">Open House Management</h2>
          <p className="opacity-90">Schedule and manage open houses with QR code check-ins</p>
        </div>

        <div className="flex justify-between items-center">
          <h3 className="text-xl font-semibold">Scheduled Open Houses</h3>
          <button
            onClick={() => setShowCreateForm(true)}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Schedule Open House
          </button>
        </div>

        {showCreateForm && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h4 className="text-lg font-semibold mb-4">Create New Open House</h4>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input
                  type="date"
                  value={newOpenHouse.date}
                  onChange={(e) => setNewOpenHouse({ ...newOpenHouse, date: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Max Attendees</label>
                <input
                  type="number"
                  value={newOpenHouse.maxAttendees}
                  onChange={(e) => setNewOpenHouse({ ...newOpenHouse, maxAttendees: parseInt(e.target.value) })}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                <input
                  type="time"
                  value={newOpenHouse.startTime}
                  onChange={(e) => setNewOpenHouse({ ...newOpenHouse, startTime: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                <input
                  type="time"
                  value={newOpenHouse.endTime}
                  onChange={(e) => setNewOpenHouse({ ...newOpenHouse, endTime: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <button
                onClick={handleCreateOpenHouse}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                disabled={!newOpenHouse.date || !newOpenHouse.startTime || !newOpenHouse.endTime}
              >
                Create Open House
              </button>
              <button
                onClick={() => setShowCreateForm(false)}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        <div className="grid gap-4">
          {openHouses.map(openHouse => (
            <div key={openHouse.id} className="bg-white rounded-lg shadow-md p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h4 className="text-lg font-semibold">Open House - {selectedProperty?.title || 'Property'}</h4>
                  <p className="text-gray-600 flex items-center mt-1">
                    <Calendar className="w-4 h-4 mr-1" />
                    {openHouse.date} | {openHouse.startTime} - {openHouse.endTime}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-600">Registered Buyers</div>
                  <div className="text-2xl font-bold text-green-600">{openHouse.registeredBuyers}</div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h5 className="font-semibold mb-1">QR Code Check-in</h5>
                    <p className="text-sm text-gray-600">Buyers scan this code to register and get property details</p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="bg-white p-3 rounded-lg border-2 border-dashed border-gray-300">
                      <QrCode className="w-12 h-12 text-gray-400" />
                    </div>
                    <div className="text-sm">
                      <div className="font-mono bg-white px-2 py-1 rounded border">{openHouse.qrCode}</div>
                      <button className="text-blue-600 hover:text-blue-800 text-xs mt-1">Download QR</button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-2">
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  View Registrations
                </button>
                <button className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-sm flex items-center gap-1">
                  <Edit className="w-4 h-4" />
                  Edit Details
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const ProfileSection = () => {
    const specializations = [
      'Residential Sales', 'Commercial Real Estate', 'Luxury Properties', 'First-Time Buyers',
      'Investment Properties', 'Condominiums', 'New Construction', 'Relocation Services',
      'Property Management', 'Land Development', 'Foreclosures', 'Short Sales'
    ];

    const languages = [
      'English', 'French', 'Spanish', 'Mandarin', 'Cantonese', 'Italian',
      'Portuguese', 'German', 'Arabic', 'Hindi', 'Punjabi', 'Korean'
    ];

    const handleProfileUpdate = (field, value) => {
      setUserProfile(prev => ({
        ...prev,
        [field]: value
      }));
    };

    const handleArrayUpdate = (field, value) => {
      setUserProfile(prev => ({
        ...prev,
        [field]: prev[field].includes(value)
          ? prev[field].filter(item => item !== value)
          : [...prev[field], value]
      }));
    };

    const handleSaveProfile = async () => {
      setIsProfileSaving(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('Profile saved:', userProfile);
        alert('Profile saved successfully!');
      } catch (error) {
        console.error('Error saving profile:', error);
        alert('Error saving profile. Please try again.');
      } finally {
        setIsProfileSaving(false);
      }
    };

    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-6 rounded-xl">
          <h2 className="text-2xl font-bold mb-2">Profile Settings</h2>
          <p className="opacity-90">Manage your professional profile and contact information</p>
        </div>

        <div className="grid gap-6">
          {/* Personal Information */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <User className="w-5 h-5 text-purple-600" />
              Personal Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                <input
                  type="text"
                  value={userProfile.firstName}
                  onChange={(e) => handleProfileUpdate('firstName', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="John"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                <input
                  type="text"
                  value={userProfile.lastName}
                  onChange={(e) => handleProfileUpdate('lastName', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Smith"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  value={userProfile.email}
                  onChange={(e) => handleProfileUpdate('email', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                <input
                  type="tel"
                  value={userProfile.phone}
                  onChange={(e) => handleProfileUpdate('phone', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="(*************"
                />
              </div>
            </div>
          </div>

          {/* Professional Information */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <Building2 className="w-5 h-5 text-purple-600" />
              Professional Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Company/Brokerage</label>
                <input
                  type="text"
                  value={userProfile.company}
                  onChange={(e) => handleProfileUpdate('company', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="ABC Realty Inc."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">License Number</label>
                <input
                  type="text"
                  value={userProfile.licenseNumber}
                  onChange={(e) => handleProfileUpdate('licenseNumber', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="RE123456"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Years of Experience</label>
                <select
                  value={userProfile.experience}
                  onChange={(e) => handleProfileUpdate('experience', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="">Select experience</option>
                  <option value="0-1">0-1 years</option>
                  <option value="2-5">2-5 years</option>
                  <option value="6-10">6-10 years</option>
                  <option value="11-15">11-15 years</option>
                  <option value="16+">16+ years</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Website</label>
                <input
                  type="url"
                  value={userProfile.website}
                  onChange={(e) => handleProfileUpdate('website', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="https://www.yourwebsite.com"
                />
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Professional Bio</label>
              <textarea
                value={userProfile.bio}
                onChange={(e) => handleProfileUpdate('bio', e.target.value)}
                rows={4}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                placeholder="Tell potential clients about your experience, approach, and what makes you unique..."
              />
            </div>
          </div>

          {/* Address Information */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <MapPin className="w-5 h-5 text-purple-600" />
              Address Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">Street Address</label>
                <input
                  type="text"
                  value={userProfile.address}
                  onChange={(e) => handleProfileUpdate('address', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="123 Main Street"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
                <input
                  type="text"
                  value={userProfile.city}
                  onChange={(e) => handleProfileUpdate('city', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Toronto"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Province</label>
                <select
                  value={userProfile.province}
                  onChange={(e) => handleProfileUpdate('province', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="">Select province</option>
                  <option value="AB">Alberta</option>
                  <option value="BC">British Columbia</option>
                  <option value="MB">Manitoba</option>
                  <option value="NB">New Brunswick</option>
                  <option value="NL">Newfoundland and Labrador</option>
                  <option value="NS">Nova Scotia</option>
                  <option value="ON">Ontario</option>
                  <option value="PE">Prince Edward Island</option>
                  <option value="QC">Quebec</option>
                  <option value="SK">Saskatchewan</option>
                  <option value="NT">Northwest Territories</option>
                  <option value="NU">Nunavut</option>
                  <option value="YT">Yukon</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                <input
                  type="text"
                  value={userProfile.postalCode}
                  onChange={(e) => handleProfileUpdate('postalCode', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="M5V 3A8"
                />
              </div>
            </div>
          </div>

          {/* Specializations */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <Star className="w-5 h-5 text-purple-600" />
              Specializations
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {specializations.map((spec) => (
                <label key={spec} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={userProfile.specializations.includes(spec)}
                    onChange={() => handleArrayUpdate('specializations', spec)}
                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <span className="text-sm text-gray-700">{spec}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Languages */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <Globe className="w-5 h-5 text-purple-600" />
              Languages Spoken
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {languages.map((lang) => (
                <label key={lang} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={userProfile.languages.includes(lang)}
                    onChange={() => handleArrayUpdate('languages', lang)}
                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <span className="text-sm text-gray-700">{lang}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={handleSaveProfile}
              disabled={isProfileSaving}
              className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="w-5 h-5" />
              {isProfileSaving ? 'Saving...' : 'Save Profile'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  const Dashboard = () => (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-8 rounded-xl">
        <h1 className="text-3xl font-bold mb-2">Sono Brokers Marketplace</h1>
        <p className="text-lg opacity-90">AI-Powered For Sale By Owner Platform</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Active Listings</h3>
            <Home className="w-8 h-8 text-blue-600" />
          </div>
          <div className="text-3xl font-bold text-blue-600">{properties.length}</div>
          <p className="text-sm text-gray-600 mt-1">Properties listed</p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Open Houses</h3>
            <Calendar className="w-8 h-8 text-green-600" />
          </div>
          <div className="text-3xl font-bold text-green-600">{openHouses.length}</div>
          <p className="text-sm text-gray-600 mt-1">Scheduled this month</p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Total Buyers</h3>
            <Users className="w-8 h-8 text-purple-600" />
          </div>
          <div className="text-3xl font-bold text-purple-600">
            {openHouses.reduce((sum, oh) => sum + oh.registeredBuyers, 0)}
          </div>
          <p className="text-sm text-gray-600 mt-1">Registered for viewings</p>
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-6">Your Properties</h2>
        <div className="grid gap-6">
          {properties.map(property => (
            <PropertyCard key={property.id} property={property} />
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="ml-10 flex space-x-8">
                <button
                  onClick={() => setActiveTab('dashboard')}
                  className={`px-3 py-2 text-sm font-medium ${activeTab === 'dashboard' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                >
                  Dashboard
                </button>
                <button
                  onClick={() => setActiveTab('ai-reports')}
                  className={`px-3 py-2 text-sm font-medium ${activeTab === 'ai-reports' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                >
                  AI Reports
                </button>
                <button
                  onClick={() => setActiveTab('open-house')}
                  className={`px-3 py-2 text-sm font-medium ${activeTab === 'open-house' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                >
                  Open Houses
                </button>
                <button
                  onClick={() => setActiveTab('profile')}
                  className={`px-3 py-2 text-sm font-medium ${activeTab === 'profile' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                >
                  Profile
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <main className="w-full py-8 px-4 sm:px-6 lg:px-8">
        {activeTab === 'dashboard' && <Dashboard />}
        {activeTab === 'ai-reports' && <AIReportsSection />}
        {activeTab === 'open-house' && <OpenHouseSection />}
        {activeTab === 'profile' && <ProfileSection />}
      </main>
    </div>
  );
}
