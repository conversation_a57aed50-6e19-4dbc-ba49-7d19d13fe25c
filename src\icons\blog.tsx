interface iconProps {
  height?: number;
  width?: number;
}

const Blog = ({ height = 32, width = 32 }: iconProps) => {
  return (
    <svg
      width={height}
      height={width}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.12597 0H23.6246C23.5873 0.0346657 23.5513 0.0706661 23.5153 0.106666L21.4886 2.13333H4.12603C2.94738 2.13333 1.9927 3.088 1.9927 4.26666V8.00015H15.6221L13.4888 10.1335H1.9927V27.7333C1.9927 28.9119 2.94738 29.8666 4.12603 29.8666H27.5927C28.7713 29.8666 29.726 28.9119 29.726 27.7333V10.3707L31.7526 8.34405C31.7886 8.30805 31.8246 8.27205 31.8593 8.23472V27.7333C31.8593 30.0893 29.9486 32 27.5927 32H4.12603C1.77005 32 -0.140625 30.0893 -0.140625 27.7333V4.26672C-0.140625 1.91074 1.76999 0 4.12597 0Z"
        fill="currentColor"
      />
      <path
        d="M6.25821 15.4668H12.2861L12.1408 16.7721C12.1101 17.0521 12.1235 17.3308 12.1781 17.6001H6.25807C5.66873 17.6001 5.19141 17.1228 5.19141 16.5335C5.19141 15.9441 5.66886 15.4668 6.25821 15.4668Z"
        fill="currentColor"
      />
      <path
        d="M5.19141 22.9329C5.19141 22.3435 5.66873 21.8662 6.25807 21.8662H25.458C26.0474 21.8662 26.5247 22.3435 26.5247 22.9329C26.5247 23.5222 26.0474 23.9995 25.458 23.9995H6.25807C5.66873 23.9995 5.19141 23.5222 5.19141 22.9329Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M30.6224 1.23699C28.9718 -0.41233 26.297 -0.41233 24.6477 1.23699L14.5972 11.2875C14.4265 11.4582 14.3185 11.6822 14.2918 11.9235L13.7332 16.949C13.6972 17.2703 13.8092 17.5916 14.0385 17.821C14.2679 18.0503 14.5892 18.1623 14.9105 18.1263L19.936 17.5676C20.1773 17.541 20.4013 17.433 20.572 17.2623L30.6225 7.21178C32.2718 5.56246 32.2717 2.88767 30.6224 1.23699ZM26.1557 2.745C26.973 1.92901 28.297 1.92901 29.1144 2.745C29.9304 3.56232 29.9304 4.88631 29.1144 5.70367L19.3277 15.489L16.001 15.8583L16.3704 12.5317L26.1557 2.745Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default Blog;
